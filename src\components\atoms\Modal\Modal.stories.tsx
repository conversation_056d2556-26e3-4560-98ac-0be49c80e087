import { Button } from '@/components/atoms/Button';
import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { openModal } from '@/utils/modal';
import { Meta, StoryObj } from '@storybook/react-vite';

const ThemedButton = withThemeProvider(Button);

const meta: Meta = {
  title: 'Atoms/Modal',
  component: () => null,
};

export default meta;

type StoryT = StoryObj;

export const Default: StoryT = {
  render: () => {
    const showModal = () => {
      const closeModal = openModal({
        title: 'Basic Modal',
        content: (
          <>
            <p>Some contents...</p>
            <p>Some contents...</p>
            <p>Some contents...</p>
          </>
        ),
        onOk: () => {
          closeModal();
        },
        onCancel: () => {
          closeModal();
        },
      });
    };

    return (
      <ThemedButton
        type="default"
        onClick={showModal}
        label="Open Modal"
      ></ThemedButton>
    );
  },
};
