import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { useState } from 'react';
import { Pagination } from './index';

const ThemedPagination = withThemeProvider(Pagination);

const meta: Meta<typeof ThemedPagination> = {
  title: 'Atoms/Pagination',
  component: ThemedPagination,
  tags: ['autodocs'],
  argTypes: {
    current: { control: 'number' },
    total: { control: 'number' },
    pageSize: { control: 'number' },
  },
  parameters: {
    docs: {
      description: {
        component:
          'ページネーションは、大量のコンテンツをより小さく、管理しやすいページに分割するために使用されます。これにより、ユーザーはコンテンツをページごとにナビゲートでき、特に長いアイテムリスト、検索結果、データテーブルに役立ちます。',
      },
    },
  },
};

export default meta;

const PaginationWithHooks = () => {
  const [pagination, setPagination] = useState({ current: 1, pageSize: 25 });

  const handlePaginationChange = (current: number, pageSize: number) => {
    setPagination({ current, pageSize });
  };

  return (
    <ThemedPagination
      current={pagination.current}
      pageSize={pagination.pageSize}
      total={1000}
      onChange={handlePaginationChange}
    />
  );
};

export const Default: StoryObj = {
  render: () => <PaginationWithHooks />,
  parameters: {
    docs: {
      description: {
        story: 'これはページネーションコンポーネントのデフォルトの状態です。',
      },
    },
  },
};
