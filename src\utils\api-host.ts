/**
 * Utility functions for getting API host in different scenarios
 */

/**
 * Get API host from environment variables
 */
export const getApiHostFromEnv = (): string => {
  return process.env.NEXT_PUBLIC_BASE_API_URL || 'http://localhost:3000/api';
};

/**
 * Get API host dynamically based on current domain
 */
export const getApiHostDynamic = (): string => {
  if (typeof window !== 'undefined') {
    const { protocol, hostname, port } = window.location;

    // Development environment
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return `${protocol}//${hostname}:${port || '3000'}/api`;
    }

    // Production environment
    return `${protocol}//${hostname}/api`;
  }

  // Server-side fallback
  return 'http://localhost:3000/api';
};

/**
 * Get API host with environment-based logic
 */
export const getApiHost = (): string => {
  // Priority 1: Environment variable
  if (process.env.NEXT_PUBLIC_BASE_API_URL) {
    return process.env.NEXT_PUBLIC_BASE_API_URL;
  }

  // Priority 2: Dynamic detection
  return getApiHostDynamic();
};

/**
 * Get API host for server-side operations
 */
export const getServerApiHost = (): string => {
  return (
    process.env.NEXT_PUBLIC_BASE_API_URL ||
    process.env.BASE_API_URL ||
    'http://localhost:3000/api'
  );
};

/**
 * Get API host based on NODE_ENV
 */
export const getApiHostByEnv = (): string => {
  const env = process.env.NODE_ENV;

  switch (env) {
    case 'development':
      return 'http://localhost:3000/api';
    case 'production':
      return (
        process.env.NEXT_PUBLIC_BASE_API_URL || 'https://api.yourdomain.com'
      );
    case 'test':
      return 'http://localhost:3000/api';
    default:
      return 'http://localhost:3000/api';
  }
};
