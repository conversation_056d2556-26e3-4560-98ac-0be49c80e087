import { Button, EditableTable } from '@/components/atoms';
import { useTranslation } from 'next-i18next';

interface ContractTabPropsI {
  contract: any[];
  setContract: (data: any[]) => void;
  contractColumns: any[];
  handleOpenContractModal: (type?: 'create' | 'edit', index?: number) => void;
}

const ContractTab = ({
  contract,
  setContract,
  contractColumns,
  handleOpenContractModal,
}: ContractTabPropsI) => {
  const { t } = useTranslation(['product-order', 'common']);

  return (
    <div>
      <Button
        onClick={() => handleOpenContractModal('create')}
        label={t('addContractInformation')}
        type="default"
        size="small"
        style={{ marginBottom: 30, marginTop: 40 }}
      />
      <EditableTable
        pagination={false}
        data={contract}
        columns={contractColumns}
        setData={setContract}
      />
    </div>
  );
};

export default ContractTab;
