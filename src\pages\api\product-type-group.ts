import type { StoreBudgetTypeI } from '@/features/products/types';
import type { NextApiRequest, NextApiResponse } from 'next';

interface ApiResponseI<T> {
  status: number;
  result: {
    data: T;
  };
  message: string;
}

const mockStoreBudgetTypes: StoreBudgetTypeI[] = [
  {
    id: 1,
    t_pms_product_fkid: 1,
    pms_color_code: 'BLACK',
    pms_color: {
      code_kbn: 'COLOR',
      code: '<PERSON><PERSON><PERSON><PERSON>',
      code_name: 'BLAC<PERSON>',
    },
    pms_size_code: 'M',
    pms_size: null,
    orders: [true, true, true, true],
    isRegistered: true,
  },
  {
    id: 2,
    t_pms_product_fkid: 1,
    pms_color_code: 'B<PERSON>C<PERSON>',
    pms_color: {
      code_kbn: 'COLOR',
      code: 'B<PERSON>C<PERSON>',
      code_name: 'B<PERSON>C<PERSON>',
    },
    pms_size_code: 'L',
    pms_size: null,
    orders: [true, false, true, true],
    isRegistered: false,
  },
  {
    id: 3,
    t_pms_product_fkid: 1,
    pms_color_code: 'WHITE',
    pms_color: {
      code_kbn: 'COLOR',
      code: 'WHITE',
      code_name: 'WHITE',
    },
    pms_size_code: 'L',
    pms_size: null,
    orders: [false, false, false, false],
    isRegistered: true,
  },
];

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponseI<StoreBudgetTypeI[]> | { message: string }>,
) {
  if (req.method === 'GET') {
    await new Promise((resolve) => setTimeout(resolve, 400));

    return res.status(200).json({
      status: 200,
      result: {
        data: mockStoreBudgetTypes,
      },
      message: 'データの取得に成功しました。',
    });
  }

  res.setHeader('Allow', ['GET']);

  return res.status(405).json({ message: `Method ${req.method} Not Allowed` });
}
