import SearchWithModal from '@/components/atoms/SearchWithModal';
import { useTranslation } from 'next-i18next';
import ProductCategoryModal from '.';
import { ProductCategoryI } from '../../types';

export const ProductCategoryField = ({
  selected,
  onSelect,
}: {
  selected: ProductCategoryI | null;
  onSelect: (value: ProductCategoryI, onClose?: () => void) => void;
}) => {
  const { t } = useTranslation('products');

  return (
    <SearchWithModal
      title={t('selectCodeInformation')}
      label={selected?.code_name ?? ''}
      textSearch={t('search')}
      content={({ onClose }) => (
        <ProductCategoryModal onSelect={(value) => onSelect(value, onClose)} />
      )}
    />
  );
};
