export interface ApiRequestI<T = any> {
  data: T;
  headers?: Record<string, string>;
  params?: Record<string, string | number | boolean>;
}

export interface ApiRequestParamsI {
  page?: number;
  per_page?: number;
  sortBy?: string;
  sortOrder?: string;
  search?: string;
}

export interface BaseApiResponseI {
  status: number;
  message?: string;
  error?: string | Record<string, any>;
}

export interface ApiResponseListI<T = any> extends BaseApiResponseI {
  result: {
    data: T[];
    meta: PaginationMetaI;
  };
}

export interface ApiResponseDetailI<T = any> extends BaseApiResponseI {
  result: T;
}

export interface UploadFileResponseI {
  path: string;
  original_name: string;
  logical_name: string;
}

export interface ApiResponseUploadI extends BaseApiResponseI {
  result: {
    files: UploadFileResponseI[];
  };
}

export interface PaginationMetaI {
  current_page: number;
  from: number;
  last_page: number;
  path: string;
  per_page: number;
  to: number;
  total: number;
}
