import { Button, Text } from '@/components/atoms';
import { Form } from '@/components/organisms/Form';
import { TFunction } from 'i18next';
import useColorSizeDetail from '../../hooks/useColorSizeDetail';
import { ProductDetailI, StoreBudgetTypeI } from '../../types';
import ViewTable from '../ColorList/ViewTable';

interface ColorSizeModalPropsI {
  product?: ProductDetailI;
  previousData: StoreBudgetTypeI;
  t: TFunction<'products'>;
  onClose?: () => void;
}

const ColorSizeModal = ({
  t,
  product,
  previousData,
  onClose,
}: ColorSizeModalPropsI) => {
  const { form, formFields, detail, handleDelete, handleSubmit } =
    useColorSizeDetail(t);

  const handleClickUpdate = async () => {
    try {
      const rs = await handleSubmit();
      if (rs) {
        onClose?.();
      }
    } catch {
      return;
    }
  };

  const handleclickDelete = async () => {
    try {
      const rs = await handleDelete();
      if (rs) {
        onClose?.();
      }
    } catch {
      return;
    }
  };

  return (
    <div className="flex w-full flex-col gap-1">
      <ViewTable
        data={[
          {
            label: t('partNumber'),
            value: `${product?.pms_product_no || ''}`,
          },
          {
            label: t('productName'),
            value: `${product?.pms_remarks || ''}`,
          },
        ]}
      />
      <div className="flex w-full flex-col">
        <p className="font-bold">{t('beforeChange')}</p>
        <ViewTable
          data={[
            {
              label: t('color'),
              value: `［${previousData.pms_color?.code_name || ''}］`,
            },
            {
              label: t('size'),
              value: `［${previousData.pms_size_code || ''}］`,
            },
          ]}
        />
      </div>
      <div className="flex w-full flex-col">
        <Text className="font-bold">{t('afterChange')}</Text>
        <Form
          name="form"
          form={form}
          formFields={formFields}
          numOfColumns={1}
        />
      </div>
      <div className="mt-2 flex flex-row gap-2">
        <Button
          type="primary"
          label={t('update')}
          onClick={handleClickUpdate}
        />
        <Button
          danger
          type="primary"
          label={t('delete')}
          onClick={handleclickDelete}
        />
      </div>
      {detail && (
        <div className="mt-2 text-xs text-gray-500">
          {t('newRegistrationDateAndTime')}: {detail?.created_at}{' '}
          {detail?.created_by?.name ? `(${detail?.created_by?.name})` : ''}
          <br />
          {t('lastUpdated')}: {detail?.updated_at}{' '}
          {detail?.updated_by?.name ? `(${detail?.updated_by?.name})` : ''}
        </div>
      )}
    </div>
  );
};

export default ColorSizeModal;
