import { antdTheme } from '@/styles/antd-theme';
import { ConfigProvider } from 'antd';
import jaJP from 'antd/locale/ja_JP';
import { ComponentType, CSSProperties, PropsWithChildren } from 'react';

function withThemeProvider<P>(
  Component: ComponentType<P>,
  wrapperStyles?: CSSProperties,
) {
  return function WithThemeProvider(props: PropsWithChildren<P>) {
    return (
      <ConfigProvider theme={antdTheme} locale={jaJP}>
        <div style={wrapperStyles}>
          <Component {...props} />
        </div>
      </ConfigProvider>
    );
  };
}

export { withThemeProvider };
