import { QueryKeysE } from '@/enums/query-keys';
import { getProductOrder } from '@/features/product-order/services';
import { useRouter } from 'next/router';
import { useQuery } from 'react-query';
import { useProductOrderCreate } from './useProductOrderCreate';

export const useProductOrderDetail = () => {
  const router = useRouter();
  const { id } = router.query;

  const query = useQuery(
    [QueryKeysE.PRODUCT_ORDERS, id],
    () => getProductOrder(Number(id)),
    {
      enabled: !!id,
    },
  );

  const {
    basicFormFields,
    handleSubmit,
    handleDelete,
    setSupplierFactories,
    isSupplierFactoryModalOpen,
    handleSubmitSupplierFactoryModal,
    handleCloseSupplierFactoryModal,
    orderConfirmationMessage,
    salesDepartmentMessage,
    processorMessage,
    inspectorMessage,
    purchasePriceColumns,
    contractColumns,
    isContractModalOpen,
    handleOpenContractModal,
    handleCloseContractModal,
    contractIndex,
    contractModalType,
    isRetailPriceModalOpen,
    handleCloseRetailPriceModal,
    setIsRetailPriceModalOpen,
  } = useProductOrderCreate();

  return {
    ...query,
    basicFormFields,
    handleSubmit,
    handleDelete,
    setSupplierFactories,
    isSupplierFactoryModalOpen,
    handleSubmitSupplierFactoryModal,
    handleCloseSupplierFactoryModal,
    orderConfirmationMessage,
    salesDepartmentMessage,
    processorMessage,
    inspectorMessage,
    purchasePriceColumns,
    contractColumns,
    isContractModalOpen,
    handleOpenContractModal,
    handleCloseContractModal,
    contractIndex,
    contractModalType,
    isRetailPriceModalOpen,
    setIsRetailPriceModalOpen,
    handleCloseRetailPriceModal,
  };
};
