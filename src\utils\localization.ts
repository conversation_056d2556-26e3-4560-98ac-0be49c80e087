import { basicAuthCheck } from '@/configs/basic-auth';
import { LanguageE } from '@/enums';
import {
  GetServerSidePropsContext,
  GetStaticPaths,
  GetStaticPropsContext,
} from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

export const getServerSideTranslationsProps =
  (namespaces: string[] = []) =>
  async (context: GetServerSidePropsContext) => {
    const { req, res, locale, params } = context;

    await basicAuthCheck(req, res);

    const translations = await serverSideTranslations(locale || LanguageE.JA, [
      ...namespaces,
      'components',
      'common',
    ]);

    return {
      props: {
        ...translations,
        ...params,
      },
    };
  };

export const getStaticTranslationsProps =
  (namespaces: string[] = []) =>
  async (context: GetStaticPropsContext) => {
    const { locale, params } = context;

    const translations = await serverSideTranslations(locale || LanguageE.JA, [
      ...namespaces,
      'components',
      'common',
    ]);

    return {
      props: {
        ...translations,
        ...params,
      },
    };
  };

export const getStaticPaths: GetStaticPaths = async () => {
  const paths: { params: { id: string } }[] = [
    {
      params: { id: 'id' },
    },
  ];

  return {
    paths,
    fallback: true,
  };
};
