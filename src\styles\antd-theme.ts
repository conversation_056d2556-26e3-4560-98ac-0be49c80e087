import { ThemeConfig } from 'antd';
import { themeColors } from './theme';

export const antdTheme: ThemeConfig = {
  token: {
    colorPrimary: themeColors.buttonAccentText,
    colorText: themeColors.mainText,
    colorTextBase: themeColors.mainText,
    colorTextSecondary: themeColors.subText,
    colorBorder: themeColors.deviderColor,
    borderRadius: 4,
  },
  components: {
    Menu: {
      itemColor: 'white',
      subMenuItemBorderRadius: 4,
      itemHoverBg: '#d4ebe4',
      itemHoverColor: themeColors.inputHover,
    },
    Input: {
      colorBorder: themeColors.inputBorder,
      colorPrimaryHover: themeColors.inputHover,
    },
    Select: {
      colorBorder: themeColors.inputBorder,
      colorPrimaryHover: themeColors.inputHover,
    },
    DatePicker: {
      colorBorder: themeColors.inputBorder,
      colorPrimaryHover: themeColors.inputHover,
    },
    Button: {
      colorPrimaryHover: themeColors.mainNavHover,
    },
    Collapse: {
      headerBg: 'white',
    },
  },
};
