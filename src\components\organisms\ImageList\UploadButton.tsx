import useLocalization from '@/hooks/useLocalization';
import { InboxOutlined } from '@ant-design/icons';
import React, { forwardRef } from 'react';

export const UploadButton = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>((props, ref) => {
  const { tComponents } = useLocalization();

  return (
    <div className="p-[20px]">
      <div
        ref={ref}
        className="absolute top-0 left-0 z-10 m-[-5px] h-[calc(100%+10px)] w-[calc(100%+10px)]"
        {...props}
      />
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">{tComponents('form.uploadAreaText')}</p>
      <p className="ant-upload-hint">{tComponents('form.uploadHint')}</p>
    </div>
  );
});

UploadButton.displayName = 'UploadButton';
