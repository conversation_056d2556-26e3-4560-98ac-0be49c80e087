import { DEFAULT_PAGE_SIZE, PAGE_SIZE_OPTIONS } from '@/constants/common';
import {
  CaretLeftOutlined,
  CaretRightOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import {
  Pagination as AntdPagination,
  type PaginationProps as AntdPaginationProps,
  Button,
} from 'antd';
import { useTranslation } from 'next-i18next';
import { useEffect, useState } from 'react';
import { Select } from '../Select';
import { Text } from '../Text';

export interface PaginationPropsI extends AntdPaginationProps {
  onRefresh?: () => void;
  extraActions?: React.ReactNode;
}

export const Pagination = ({
  current,
  total,
  pageSize: initialPageSize,
  onChange,
  onRefresh,
  ...props
}: PaginationPropsI) => {
  const [pageSize, setPageSize] = useState(
    initialPageSize || DEFAULT_PAGE_SIZE,
  );
  const { t } = useTranslation('components');

  const totalPages = Math.ceil(total! / pageSize!);

  const handlePageSizeChange = (value: number) => {
    setPageSize(value);
    onChange?.(1, value);
  };

  const handlePageChange = (page: number, newPageSize: number) => {
    if (pageSize !== newPageSize) {
      setPageSize(newPageSize);
    }

    onChange?.(page, newPageSize);
  };

  const goToFirstPage = () => {
    onChange?.(1, pageSize);
  };

  const goToLastPage = () => {
    onChange?.(totalPages, pageSize);
  };

  useEffect(() => {
    if (initialPageSize) {
      setPageSize(initialPageSize);
    }
  }, [initialPageSize]);

  return (
    <div className="flex flex-wrap items-center justify-between gap-y-[12px]">
      <div className="flex flex-wrap items-center justify-between gap-x-[20px] gap-y-[12px]">
        <div className="flex items-center space-x-4 text-sm font-bold">
          <span>
            {t('pagination.totalItems', { total: total?.toLocaleString() })}
          </span>
          <span>{t('pagination.displayPages', { current, totalPages })}</span>
        </div>

        <div className="flex flex-wrap items-center space-x-[20px] gap-y-[12px]">
          <Select
            value={pageSize}
            onChange={handlePageSizeChange}
            options={PAGE_SIZE_OPTIONS}
            className="!mr-[5px] !h-[26px] !w-[70px] !rounded-[100px] !text-[14px]"
          />
          <Text>{t('pagination.rows')}</Text>
          <Button
            className="!h-[26px] !rounded-[100px]"
            icon={<ReloadOutlined />}
            onClick={onRefresh}
          >
            {t('pagination.update')}
          </Button>
          <div>{props.extraActions}</div>
        </div>
      </div>

      <div className="flex !items-center justify-end space-x-2">
        <Button
          onClick={goToFirstPage}
          disabled={current === 1}
          className="!h-[26px] !rounded-[100px]"
          icon={<CaretLeftOutlined />}
        >
          {t('pagination.toFirst')}
        </Button>

        <AntdPagination
          current={current}
          total={total}
          pageSize={pageSize}
          onChange={handlePageChange}
          showSizeChanger={false}
          className="custom-pagination mt-0.5 h-[26px] text-[14px]"
          responsive={true}
          showLessItems
          {...props}
        />

        <Button
          onClick={goToLastPage}
          disabled={current === totalPages}
          className="!h-[26px] !rounded-[100px] text-[14px]"
          icon={<CaretRightOutlined />}
          iconPosition="end"
        >
          {t('pagination.toLast')}
        </Button>
      </div>
    </div>
  );
};
