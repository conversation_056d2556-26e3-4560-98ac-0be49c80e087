import { CaretRightOutlined } from '@ant-design/icons';
import { Collapse as AntCollapse, CollapseProps } from 'antd';
import React from 'react';
import { Text } from '../Text';

export const Collapse: React.FC<CollapseProps> = (props) => {
  return (
    <AntCollapse
      expandIcon={({ isActive }) => (
        <CaretRightOutlined rotate={isActive ? 90 : 0} />
      )}
      {...props}
      items={props?.items?.map((item) => ({
        ...item,
        label: <Text className="!font-bold">{item.label}</Text>,
      }))}
    />
  );
};
