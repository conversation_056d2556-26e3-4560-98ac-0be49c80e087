import { FormFieldConfigI } from '@/components/organisms/Form';
import { ApiEndpointE } from '@/enums';
import { SelectE } from '@/enums/select';
import { handleConfirmSubmit } from '@/utils';
import { Form as AntdForm, message } from 'antd';
import { TFunction } from 'i18next';

const useColorSizeDetail = (t: TFunction<'products'>) => {
  const [form] = AntdForm.useForm();
  const formFields: FormFieldConfigI[] = [
    {
      label: t('color'),
      name: 'color',
      type: 'select',
      apiConfig: {
        endpoint: ApiEndpointE.MASTER_CODE,
        params: { code_kbn: [SelectE.M_CODE_COLOR], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      rules: [{ required: true, message: t('colorRequiredFields') }],
      requiredText: t('required'),
      labelPosition: 'right',
    },
    {
      label: t('size'),
      name: 'size',
      type: 'select',
      apiConfig: {
        endpoint: ApiEndpointE.MASTER_CODE,
        params: { code_kbn: [SelectE.M_CODE_SIZE], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      rules: [{ required: true, message: t('sizeRequiredFields') }],
      requiredText: t('required'),
      labelPosition: 'right',
    },
  ];
  const detail = {
    created_at: '2023/03/14 13:38:57',
    created_by: { name: 'SYSTEM システムサポート)' },
    updated_at: '2024/12/16 11:56:41',
    updated_by: { name: 'kenji_tsuboi 坪井 建治' },
  };

  const handleSubmit = async () => {
    try {
      await form.validateFields();
      // Implement edit logic here
      const confirm = await handleConfirmSubmit(t('askUpdate'));
      if (!confirm) {
        return false;
      }
      message.success(t('updateCompleted'));

      return true;
    } catch {
      return false;
    }
  };

  const handleDelete = async () => {
    // Implement delete logic here
    try {
      const confirm = await handleConfirmSubmit(t('askDelete'));
      if (!confirm) {
        return false;
      }
      message.success(t('deleteCompleted'));

      return true;
    } catch {
      return false;
    }
  };

  return { form, formFields, detail, handleSubmit, handleDelete };
};

export default useColorSizeDetail;
