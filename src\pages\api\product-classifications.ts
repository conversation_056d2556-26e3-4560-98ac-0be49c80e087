import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from '@/constants/common';
import { ProductClassificationDataI } from '@/features/product-information/types';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method === 'GET') {
    const { page = DEFAULT_PAGE, per_page = DEFAULT_PAGE_SIZE } = req.query;

    await new Promise((resolve) => setTimeout(resolve, 300));

    // Mock data for product categories
    const data: ProductClassificationDataI[] = [
      { id: 1, code: 'coca', classification: 'ブランド', group: 'coca' },
      { id: 2, code: 'comcoca', classification: 'ブランド', group: 'comcoca' },
      { id: 3, code: 'pepsi', classification: 'ブランド', group: 'pepsi' },
      {
        id: 4,
        code: 'fashion',
        classification: 'ファッション',
        group: 'fashion-group',
      },
      {
        id: 5,
        code: 'beauty',
        classification: 'ビューティー',
        group: 'beauty-group',
      },
      {
        id: 6,
        code: 'sports',
        classification: 'スポーツ',
        group: 'sports-group',
      },
      {
        id: 7,
        code: 'outdoor',
        classification: 'アウトドア',
        group: 'outdoor-group',
      },
      { id: 8, code: 'home', classification: 'ホーム', group: 'home-group' },
      {
        id: 9,
        code: 'electronics',
        classification: 'エレクトロニクス',
        group: 'electronics-group',
      },
      { id: 10, code: 'food', classification: 'フード', group: 'food-group' },
      {
        id: 11,
        code: 'drink',
        classification: 'ドリンク',
        group: 'drink-group',
      },
      {
        id: 12,
        code: 'health',
        classification: 'ヘルス',
        group: 'health-group',
      },
    ];

    // Apply pagination
    const start = (Number(page) - 1) * Number(per_page);
    const end = start + Number(per_page);
    const paginatedData = data.slice(start, end);
    const sumOfPage = Math.ceil(data.length / Number(per_page));

    res.status(200).json({
      message: 'データの取得に成功しました。',
      result: {
        data: paginatedData,
        meta: {
          current_page: Number(page),
          from: start + 1,
          last_page: sumOfPage,
          per_page: Number(per_page),
          to: end,
          total: data.length,
        },
      },
    });
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
