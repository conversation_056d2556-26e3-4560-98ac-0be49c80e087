export enum ProductFormFieldKeyE {
  PRODUCT_CODE = 'pms_product_no',
  PRODUCT_NAME = 'pms_product_name',
  GENDER_CATEGORY = 'gender_category_code',
  PRODUCT_CATEGORY = 'product_category_code',
  STORE_BUDGET_CATEGORY = 'm_store_budget_type_fkid',
  SALES_PERIOD = 'sales_period',
  SALES_PLAN_START = 'pms_sales_plan_start_datetime',
  SALES_PLAN_END = 'pms_sales_plan_end_datetime',
  STORE_WITHDRAW_DATE = 'pms_store_withdraw_datetime',
  SALES_SEASON_CODE = 'pms_sales_season_code',
  SAMPLE_NO = 'pms_sample_no',
  PLANNER_CODE = 'pms_planner_code',
  HS_CODE = 'pms_hs_code',
  REMARKS = 'pms_remarks',
  PRODUCT_TYPE_GROUP = 'product_type_group',
  PRODUCT_TYPE_NAME = 'product_type_name',
}
