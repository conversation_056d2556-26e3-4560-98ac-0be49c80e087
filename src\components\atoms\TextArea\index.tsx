import { Input as AntdInput } from 'antd';
import { TextAreaProps as AntdTextAreaProps } from 'antd/es/input';
import { styled } from 'styled-components';

const StyledTextArea = styled(AntdInput.TextArea)`
  box-shadow: var(--inner-box-shadow);
  border: var(--boder);
  resize: none;
` as typeof AntdInput.TextArea;

export type TextAreaPropsT = AntdTextAreaProps;

export const TextArea = (props: TextAreaPropsT) => {
  return <StyledTextArea {...props} />;
};
