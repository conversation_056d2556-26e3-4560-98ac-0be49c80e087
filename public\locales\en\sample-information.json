{"sampleInformationList": "Sample Information List", "sampleInformationDetail": "Sample Information Details", "sampleInformationAddition": "Add Sample Information", "sampleNumberNotesInput": "Enter Sample No., Notes", "detailedSearch": "Detailed Search", "pagerTotalCount": "Pager / Total Count", "no": "No", "sampleInformation": "sample Information", "copy": "Copy", "image": "Image", "sampleNumber": "Sample Number", "orderTarget": "Order Target", "receivedRefund": "Received/Refunded", "gender": "Gender", "deliveryDate": "Request Date", "customer": "Customer", "buyer": "Buyer", "notes": "Notes", "bulkUpdate": "Bulk Update", "startDate": "start Date", "endDate": "end Date", "numberMultipleHint": "※If specifying multiple samples, separate them with line breaks.", "requestDate": "Sample Request Date", "planningDate": "Planning Month", "partner": "Partner", "status": "Status", "order": "Order", "notOrdered": "Not Ordered", "placeAnOrder": "Place Order", "receipt": "Receipt", "unreceived": "Not Received", "received": "Received", "payment": "Payment", "paidStatus": "Paid", "paid": "Paid", "notPaid": "Not Paid", "preparation": "Notes (Partial Match)", "productCategory": "Product Category", "genderCategory": "Gender Category", "advancedSearch": "Advanced Search", "search": "Search", "clear": "clear", "selectDate": "select date", "genders": {"men": "Men", "woman": "Woman", "kids_boys": "Kids (Boys)", "kids_girls": "Kids (Girls)"}, "return": "Return", "basic": "Basic", "material": "Material", "fabric": "<PERSON><PERSON><PERSON>", "sampleImage": "Sample Image", "patternFiles": "Pattern Files", "plainingDate": "Planning Date", "sampleRequestDate": "Sample request date", "planner": "Planner", "category": "Category", "trend": "Trend", "taste": "Taste", "originalLink": "Original Link", "estimatedPrice": "Estimated Price", "creationCount": "Creation Count", "remarks": "Remarks", "registration": "Registration", "delete": "Delete", "currency": "<PERSON><PERSON><PERSON><PERSON>", "pleaseEnterRequiredFields": "Please enter required fields", "pleaseEnterCorrectFormat": "Please enter correct format（yyyy/mm/dd）", "partnerFactory": "Partner Factory", "closeAll": "Close All", "openAll": "Open All", "update": "Update", "updateFailed": "Update Failed", "updateCompleted": "Update Completed", "formSearchError": "There is an error in the input. Please check", "noDataFound": "No data found", "multiInputHint": "Separate multiple entries with line breaks .", "noDataToUpdate": "No data to update.", "maxLength": "The maximum length is {maxLength} characters", "priceIntegerDecimalLimit": "Please enter a value with up to {{integer}} digits for the integer part and {{decimal}} digits for the decimal part.", "newRegistrationDateAndTime": "New registration date and time", "lastUpdated": "Last updated", "fabricUsedAreas": "Fabric used areas", "fabricManagementNumberReviewMeeting": "Fabric Management Number Review Meeting", "fabricName": "Fabric name", "addMaterial": "Add Material", "addAPartnerFactory": "Add a Pattern Factory"}