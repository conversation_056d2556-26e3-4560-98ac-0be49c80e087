'use client';

import { But<PERSON> } from '@/components/atoms';
import { DateRange } from '@/components/atoms/DateRange';
import { Form, FormFieldConfigI } from '@/components/organisms/Form';
import { MAX_LABEL_LENGTH } from '@/constants/common';
import { QueryKeysE } from '@/enums/query-keys';
import {
  CodeKbnE,
  SampleFormFieldKeyE,
} from '@/features/sample-information/enums';
import type { SampleSearchFormValuesI } from '@/features/sample-information/types';
import { getMasterCode } from '@/services/common';
import { MasterCodeOptionI } from '@/types/common';
import { DateHelper, StringUtil } from '@/utils';
import { ClearOutlined, SearchOutlined } from '@ant-design/icons';
import { Form as AntdForm, Grid, message } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { useQuery } from 'react-query';
import { getSupplier } from '../../services';

type SampleSearchFormPropsT = {
  onSearch?: (values: SampleSearchFormValuesI) => void;
};

export default function SampleSearchForm({ onSearch }: SampleSearchFormPropsT) {
  const { t } = useTranslation('sample-information');
  const [form] = AntdForm.useForm();
  const { useBreakpoint } = Grid;

  const [buyerOptions, setBuyerOptions] = useState<MasterCodeOptionI[]>([]);
  const [genderCategoryOptions, setGenderCategoryOptions] = useState<
    MasterCodeOptionI[]
  >([]);
  const [productCategoryOptions, setProductCategoryOptions] = useState<
    MasterCodeOptionI[]
  >([]);
  const [supplierOptions, setSupplierOptions] = useState<MasterCodeOptionI[]>(
    [],
  );

  const fetchMasterOptions = async (codeKbn: string[]) => {
    const response = await getMasterCode({ code_kbn: codeKbn });

    return (response?.result || []).map((item: any) => ({
      label: item.code_name,
      value: item.code,
    }));
  };

  useQuery([QueryKeysE.BUYER], () => fetchMasterOptions([CodeKbnE.BUYER]), {
    onSuccess: (options) => setBuyerOptions(options),
  });

  useQuery(
    [QueryKeysE.PRODUCT_CATEGORY],
    () => fetchMasterOptions([CodeKbnE.PRODUCT_CATERGORY]),
    {
      onSuccess: (options) => setProductCategoryOptions(options),
    },
  );

  useQuery(
    [QueryKeysE.GENDER_CATEGORY],
    () => fetchMasterOptions([CodeKbnE.GENDER_CATEGORY]),
    {
      onSuccess: (options) => setGenderCategoryOptions(options),
    },
  );

  useQuery(
    [QueryKeysE.SUPPLIER],
    async () => {
      const response = await getSupplier({ hidden_flg: '0' });

      return (response?.result || []).map((item: any) => ({
        label: item.pms_supplier_name,
        value: item.pms_supplier_code,
      }));
    },
    {
      onSuccess: (options) => setSupplierOptions(options),
    },
  );

  const triggerSearch = async () => {
    try {
      const values = await form.validateFields();
      onSearch?.(values);
    } catch {
      message.error(t('formSearchError'));
    }
  };

  const handleClear = () => {
    form.resetFields();
    onSearch?.({});
  };

  const formFields: FormFieldConfigI[] = [
    {
      label: t('sampleNumber'),
      name: SampleFormFieldKeyE.SAMPLE_NUMBERS,
      type: 'textarea',
      extra: <p style={{ marginTop: '10px' }}>{t('multiInputHint')}</p>,
    },
    {
      label: t('buyer'),
      name: SampleFormFieldKeyE.PLANNER,
      type: 'select',
      options: buyerOptions.map((opt) => ({
        value: opt.value,
        label: StringUtil.truncateText(opt.label, MAX_LABEL_LENGTH),
      })),
    },
    {
      label: t('genderCategory'),
      name: SampleFormFieldKeyE.GENDER_CATEGORY,
      type: 'select',
      initialValue: '',
      options: genderCategoryOptions.map((opt) => ({
        value: opt.value,
        label: StringUtil.truncateText(opt.label, MAX_LABEL_LENGTH),
      })),
    },
    {
      label: t('requestDate'),
      name: SampleFormFieldKeyE.REQUEST_DATE,
      type: 'datepicker-range',
      render: () => (
        <DateRange
          fromName={SampleFormFieldKeyE.REQUEST_DATE_FROM}
          toName={SampleFormFieldKeyE.REQUEST_DATE_TO}
          placeholder={[t('startDate'), t('endDate')]}
          format={DateHelper.FORMAT.FORMAT_2}
        />
      ),
    },
    {
      label: t('productCategory'),
      name: SampleFormFieldKeyE.PRODUCT_CATEGORY,
      type: 'select',
      options: productCategoryOptions.map((opt) => ({
        value: opt.value,
        label: StringUtil.truncateText(opt.label, MAX_LABEL_LENGTH),
      })),
    },
    {
      label: t('planningDate'),
      name: SampleFormFieldKeyE.PLANNING_DATE,
      type: 'datepicker',
      placeholder: t('selectDate'),
      picker: 'month',
      format: DateHelper.FORMAT.FORMAT_33,
    },
    {
      label: t('partner'),
      name: SampleFormFieldKeyE.PARTNER,
      type: 'select',
      options: supplierOptions.map((opt) => ({
        value: opt.value,
        label: StringUtil.truncateText(opt.label, MAX_LABEL_LENGTH),
      })),
    },
    {
      label: t('order'),
      name: SampleFormFieldKeyE.ORDER,
      type: 'checkboxgroup',
      initialValue: [],
      options: [
        { label: t('notOrdered'), value: 0 },
        { label: t('placeAnOrder'), value: 1 },
      ],
    },
    {
      label: t('preparation'),
      name: SampleFormFieldKeyE.PREPARATION,
      type: 'input',
      maxLength: 50,
    },
    {
      label: t('receipt'),
      name: SampleFormFieldKeyE.RECEIPT,
      type: 'checkboxgroup',
      initialValue: [],
      options: [
        { label: t('unreceived'), value: 0 },
        { label: t('received'), value: 1 },
      ],
    },
    {
      label: '',
      name: '',
      type: SampleFormFieldKeyE.EMPTY,
      options: [],
    },
    {
      label: t('payment'),
      name: SampleFormFieldKeyE.PAYMENT,
      type: 'checkboxgroup',
      initialValue: [],
      options: [
        { label: t('notPaid'), value: 0 },
        { label: t('paidStatus'), value: 1 },
      ],
    },
  ];

  const ResponsiveForm = () => {
    const screens = useBreakpoint();

    const numberOfColumns = screens.lg ? 2 : 1;

    const formFields
    return (
      <Form
        form={form}
        formFields={formFields}
        numOfColumns={numberOfColumns}
        onFinish={triggerSearch}
        initialValues={{
          [SampleFormFieldKeyE.REQUEST_DATE_FROM]: dayjs().subtract(6, 'month'),
        }}
        onFinishFailed={(err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error('Validation failed:', err);
          }
        }}
      />
    );
  };

  return (
    <div>
      {ResponsiveForm()}
      <div className="mt-4 flex gap-4">
        <Button
          icon={<SearchOutlined />}
          label={t('advancedSearch')}
          type="primary"
          onClick={triggerSearch}
        />
        <Button
          icon={<ClearOutlined />}
          label={t('clear')}
          type="default"
          onClick={handleClear}
        />
      </div>
    </div>
  );
}
