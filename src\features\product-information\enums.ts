export enum CodeKbnE {
  BUYER = 'BUYER',
  PRODUCT_CATEGORY = 'TMPCATECN',
  GENDER_CATEGORY = 'GENDERCATEGORYCD',
  COLOR = 'COLOR',
  SIZE = 'SIZE',
  ORDER_TYPE = 'PMSORDERTYPE',
  PRODUCT_TYPE = 'PMSPRODUCTTYPE',
}

export enum ProductFormFieldKeyE {
  NO = 'no',
  PRODUCT_IMAGE = 'product_image',
  PRODUCT_NO = 'product_no',
  PRODUCT_NAME = 'product_name',
  SAMPLE_NO = 'sample_no',
  PLANNER = 'planner_code',
  GENDER_CATEGORY = 'gender_category_code',
  PRODUCT_CATEGORY = 'product_category_code',
  REMARKS = 'product_remarks',
  PARTNER = 'supplier_code',
  SELECTION = 'selection',
  GROUP = 'group',
  CLASSIFICATION = 'classification',
  LOCAL_SHIPPING_PLAN_DATETIME = 'local_shipping_plan_datetime',
  LOCAL_SHIPPING_PLAN_DATETIME_FROM = 'local_shipping_plan_datetime_from',
  LOCAL_SHIPPING_PLAN_DATETIME_TO = 'local_shipping_plan_datetime_to',
  LOCAL_SHIPPING_ACTUAL_DATETIME = 'local_shipping_result_datetime',
  LOCAL_SHIPPING_ACTUAL_DATETIME_FROM = 'local_shipping_result_datetime_from',
  LOCAL_SHIPPING_ACTUAL_DATETIME_TO = 'local_shipping_result_datetime_to',
  PRODUCT_TYPE = 'product_type_seq',
  FABRIC_NO = 'fabric_seq_no',
  FABRIC_NAME = 'fabric_name',
  COLOR = 'color',
  SIZE = 'size',
  ORDER_TYPE = 'order_type',
  STORE_ARRIVAL_PLAN_DATETIME = 'store_arrival_plan_datetime',
  STORE_ARRIVAL_PLAN_DATETIME_FROM = 'store_arrival_plan_datetime_from',
  STORE_ARRIVAL_PLAN_DATETIME_TO = 'store_arrival_plan_datetime_to',
  STORE_ARRIVAL_RESULT_DATETIME = 'store_arrival_result_datetime',
  STORE_ARRIVAL_RESULT_DATETIME_FROM = 'store_arrival_result_datetime_from',
  STORE_ARRIVAL_RESULT_DATETIME_TO = 'store_arrival_result_datetime_to',
}
