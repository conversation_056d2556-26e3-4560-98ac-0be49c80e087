import { Spin, type PaginationProps as AntdPaginationProps } from 'antd';
import { useMemo } from 'react';
import { ColumnDefinition, ReactTabulator } from 'react-tabulator';
import 'react-tabulator/css/tabulator.min.css';
import 'react-tabulator/css/tabulator_bootstrap5.min.css';
import { ReactTabulatorProps } from 'react-tabulator/lib/ReactTabulator';
import styled from 'styled-components';
import { Pagination } from '../Pagination';

const StyledTabulator = styled(ReactTabulator)`
  border-right: 1px solid #e5e7eb !important;
  border-left: 1px solid #e5e7eb !important;

  border-radius: 4px !important;
  overflow: hidden !important;

  .tabulator-cell {
    border-right: 1px solid #e5e7eb !important;
    border-bottom: 1px solid #e5e7eb !important;
  }

  .tabulator-header .tabulator-col {
    border-right: 1px solid #e5e7eb !important;
    background-color: #f9fafb !important;
  }
  .tabulator-header .tabulator-col-title {
    text-align: ${(props) => props.headerAlign || 'left'};
  }

  .tabulator-row {
    border-bottom: 1px solid #e5e7eb !important;

    &:hover {
      background-color: white !important;
    }
  }

  .tabulator-row.tabulator-row-even {
    background-color: white !important;
  }

  .tabulator-row .tabulator-cell:last-child {
    border-right: none !important;
  }

  .tabulator-row .tabulator-cell.tabulator-editing {
    border: 1px solid var(--button-accent-text) !important;
  }

  .tabulator-placeholder {
    padding: 20px;
    text-align: center;
    color: var(--sub-text);
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px !important;
  }
`;

export interface TableOptionsI extends ReactTabulatorProps {
  cellEdited?: (cell: any) => void;
  resizableColumnGuide?: boolean;
  resizableColumnFit?: boolean;
  columnDefaults?: {
    resizable?: boolean;
    headerSort?: boolean;
  };
  layout?: 'fitColumns' | 'fitData' | 'fitDataFill';
  movableColumns?: boolean;
  placeholder?: string;
}

interface TablePropsI<T> {
  data: T[];
  columns: ColumnDefinition[];
  tableOptions?: TableOptionsI;
  pagination: AntdPaginationProps | boolean;
  style?: React.CSSProperties;
  loading?: boolean;
  setData: (data: T[]) => void;
  onRefresh?: () => void;
  placeholder?: string;
  headerAlign?: 'center' | 'left' | 'right';
}

export const EditableTable = <T,>({
  data,
  setData,
  onRefresh,
  pagination,
  columns,
  style,
  tableOptions,
  loading,
  placeholder = 'データがありません',
  ...rest
}: TablePropsI<T>) => {
  const { totalItems, startIndex, currentPageData } = useMemo(() => {
    if (typeof pagination === 'object' && pagination !== null) {
      const pageSize = Number(pagination.pageSize);
      const totalItems = data?.length;
      const startIndex = (Number(pagination.current) - 1) * pageSize;
      const endIndex = Math.min(startIndex + pageSize, totalItems);
      const currentPageData = data?.slice(startIndex, endIndex);

      return {
        totalItems,
        startIndex,
        endIndex,
        currentPageData,
      };
    }

    // If pagination is false, show all data
    return {
      totalItems: data?.length,
      startIndex: 0,
      endIndex: data?.length,
      currentPageData: data,
    };
  }, [data, pagination]);

  const options = useMemo(() => {
    return {
      cellEdited: function (cell: any) {
        const updatedData = cell.getTable().getData();
        const newData = [...data];
        currentPageData.forEach((item: any, index: number) => {
          newData[startIndex + index] = updatedData[index];
        });
        setData(newData);
      },
      resizableColumnGuide: true,
      resizableColumnFit: true,
      columnDefaults: {
        resizable: true,
        headerSort: false,
      },
      layout: 'fitColumns',
      movableColumns: true,
      loading,
      loadingElement: loading
        ? "<div class='tabulator-loading'><div class='tabulator-loader'></div></div>"
        : '',
      placeholder: placeholder,
      ...tableOptions,
    };
  }, [
    data,
    currentPageData,
    startIndex,
    setData,
    tableOptions,
    loading,
    placeholder,
  ]);

  return (
    <div className="w-full space-y-[20px]">
      {typeof pagination === 'object' && pagination !== null && (
        <Pagination onRefresh={onRefresh} {...pagination} total={totalItems} />
      )}

      <div className="relative">
        <StyledTabulator
          data={currentPageData}
          columns={columns}
          layout="fitColumns"
          options={options}
          style={{
            width: '100%',
            minHeight: '200px',
            ...style,
          }}
          {...rest}
        />
        {loading && (
          <div
            className="absolute inset-0 flex items-center justify-center bg-white/50"
            style={{ minHeight: '200px' }}
          >
            <Spin />
          </div>
        )}
      </div>

      {typeof pagination === 'object' && pagination !== null && (
        <Pagination onRefresh={onRefresh} {...pagination} total={totalItems} />
      )}
    </div>
  );
};
