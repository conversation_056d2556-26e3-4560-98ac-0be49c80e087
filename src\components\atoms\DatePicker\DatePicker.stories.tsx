import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { Meta, StoryObj } from '@storybook/react-vite';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import { RangePicker as CustomRangePicker, DatePicker } from '.';

const StorybookDatePicker = withThemeProvider(DatePicker);
const StorybookRangePicker = withThemeProvider(CustomRangePicker);

const meta: Meta<typeof DatePicker> = {
  title: 'Atoms/DatePicker',
  component: StorybookDatePicker,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          '日付ピッカーは、ユーザーがカレンダーから日付を選択できるグラフィカルユーザーインターフェース要素です。これは、ユーザーが日付を入力する必要があるフォームやアプリケーションで一般的な要素です。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof DatePicker>;

export const Default: StoryT = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          'デフォルトの日付ピッカーでは、ユーザーがカレンダーから単一の日付を選択できます。',
      },
    },
  },
};

export const Disabled: StoryT = {
  args: {
    disabled: true,
  },
  parameters: {
    docs: {
      description: {
        story: '操作できない無効な日付ピッカー。',
      },
    },
  },
};

export const RangePicker: StoryObj<typeof CustomRangePicker> = {
  render: (args: RangePickerProps) => <StorybookRangePicker {...args} />,
  parameters: {
    docs: {
      description: {
        story: '範囲ピッカーでは、ユーザーが日付の範囲を選択できます。',
      },
    },
  },
};

export const TimePicker: StoryT = {
  args: {
    picker: 'time',
  },
  parameters: {
    docs: {
      description: {
        story: 'タイムピッカーでは、日付に加えて時刻を選択できます。',
      },
    },
  },
};

export const MonthPicker: StoryT = {
  args: {
    picker: 'month',
  },
  parameters: {
    docs: {
      description: {
        story: '月ピッカーでは、ユーザーが月と年を選択できます。',
      },
    },
  },
};

export const WeekPicker: StoryT = {
  args: {
    picker: 'week',
  },
  parameters: {
    docs: {
      description: {
        story: '週ピッカーでは、ユーザーが年の週を選択できます。',
      },
    },
  },
};

export const QuarterPicker: StoryT = {
  args: {
    picker: 'quarter',
  },
  parameters: {
    docs: {
      description: {
        story: '四半期ピッカーでは、ユーザーが年の四半期を選択できます。',
      },
    },
  },
};

export const YearPicker: StoryT = {
  args: {
    picker: 'year',
  },
  parameters: {
    docs: {
      description: {
        story: '年ピッカーでは、ユーザーが年を選択できます。',
      },
    },
  },
};

export const DefaultValue: StoryT = {
  args: {
    defaultValue: dayjs('2023-01-01'),
  },
  parameters: {
    docs: {
      description: {
        story: '日付ピッカーには、デフォルトの日付を事前に選択できます。',
      },
    },
  },
};

export const DisabledDate: StoryT = {
  args: {
    disabledDate: (current) => {
      return current && current.valueOf() > Date.now();
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          '日付ピッカーでは、特定の日付を無効にして選択できないようにすることができます。',
      },
    },
  },
};
