import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { faker } from '@faker-js/faker';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { useState, type Key } from 'react';
import { Table } from './index';

const ThemedTable = withThemeProvider(Table);

const meta: Meta<typeof ThemedTable> = {
  title: 'Atoms/Table',
  component: ThemedTable,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'テーブルは、構造化された表形式でデータを表示するために使用されるコンポーネントです。行と列で構成されており、ユーザーがデータを簡単に表示、比較、分析できます。テーブルは、財務レポート、ユーザーリスト、製品カタログなどの大規模なデータセットを提示するためによく使用されます。',
      },
    },
  },
};

export default meta;

const columns = [
  { title: '顧客番号', dataIndex: 'customerNumber', key: 'customerNumber' },
  {
    title: '顧客名',
    dataIndex: 'customerName',
    key: 'customerName',
    render: (text: string) => <a href="#">{text}</a>,
  },
  { title: '本社TEL', dataIndex: 'tel', key: 'tel' },
  { title: '本社都道府県', dataIndex: 'prefecture', key: 'prefecture' },
  { title: '事業所名', dataIndex: 'officeName', key: 'officeName' },
  { title: '担当者名', dataIndex: 'contactPerson', key: 'contactPerson' },
  {
    title: '代表者名',
    dataIndex: 'representativeName',
    key: 'representativeName',
  },
  { title: '契約情報', dataIndex: 'contractInfo', key: 'contractInfo' },
];

const createData = (index: number) => ({
  key: `${index}`,
  customerNumber: faker.string.alphanumeric(10).toUpperCase(),
  customerName: faker.company.name(),
  tel: faker.phone.number(),
  prefecture: faker.location.state({ abbreviated: false }),
  officeName: Math.random() > 0.5 ? faker.company.name() : '',
  contactPerson: faker.person.fullName(),
  representativeName: faker.person.fullName(),
  contractInfo: Math.random() > 0.5 ? 'MON 経営力向上計画FMB' : '',
});

const dataSource = Array.from({ length: 100 }, (_, i) => createData(i + 1));

const TableWithHooks = () => {
  const [pagination, setPagination] = useState({ current: 2, pageSize: 5 });

  const handlePaginationChange = (current: number, pageSize: number) => {
    setPagination({ current, pageSize });
  };

  const paginatedData = dataSource.slice(
    (pagination.current - 1) * pagination.pageSize,
    pagination.current * pagination.pageSize,
  );

  return (
    <ThemedTable
      columns={columns}
      dataSource={paginatedData}
      onRefresh={() => {
        alert('Refreshed!');
      }}
      pagination={{
        ...pagination,
        total: dataSource.length,
        onChange: handlePaginationChange,
      }}
    />
  );
};

export const Default: StoryObj = {
  render: () => <TableWithHooks />,
  parameters: {
    docs: {
      description: {
        story:
          'これはテーブルコンポーネントのデフォルトの状態です。ページネーションと更新ボタンが含まれています。',
      },
    },
  },
};

export const Loading: StoryObj = {
  render: () => (
    <ThemedTable
      columns={columns}
      dataSource={dataSource.slice(0, 5)}
      loading
      pagination={{
        total: dataSource.length,
      }}
    />
  ),
  parameters: {
    docs: {
      description: {
        story:
          'これはテーブルコンポーネントのローディング状態です。データの取得中にローディングインジケーターが表示されます。',
      },
    },
  },
};

export const EmptyData: StoryObj = {
  render: () => (
    <ThemedTable
      columns={columns}
      dataSource={[]}
      pagination={{
        total: 0,
      }}
    />
  ),
  parameters: {
    docs: {
      description: {
        story:
          'これはテーブルコンポーネントの空の状態です。表示するデータがないことを示すメッセージが表示されます。',
      },
    },
  },
};

export const NoPagination: StoryObj = {
  render: () => (
    <ThemedTable
      columns={columns}
      dataSource={dataSource.slice(0, 5)}
      pagination={false}
    />
  ),
  parameters: {
    docs: {
      description: {
        story:
          'これはページネーションのないテーブルです。ページネーションコントロールは非表示になっています。',
      },
    },
  },
};

const RowSelectionTable = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);

  const onSelectChange = (newSelectedRowKeys: Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  return (
    <ThemedTable
      rowSelection={rowSelection}
      columns={columns}
      dataSource={dataSource.slice(0, 5)}
      pagination={false}
    />
  );
};

export const WithRowSelection: StoryObj = {
  render: () => <RowSelectionTable />,
  parameters: {
    docs: {
      description: {
        story:
          'これは行選択付きのテーブルです。ユーザーが1つ以上の行を選択できます。',
      },
    },
  },
};
