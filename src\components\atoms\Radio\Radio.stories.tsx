import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { Meta, StoryObj } from '@storybook/react-vite';
import { useState } from 'react';
import { Radio } from './index';

const ThemedRadio = withThemeProvider(Radio);

const meta: Meta<typeof Radio> = {
  title: 'Atoms/Radio',
  component: ThemedRadio,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'チェックボックスを使用すると、ユーザーは選択肢のリストから1つ以上のオプションを選択できます。ラジオボタンとは異なり、ユーザーはグループ内の任意の数のチェックボックスを選択でき、何も選択しないこともすべて選択することも可能です。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Radio>;

export const Default: StoryT = {
  args: {
    label: 'Radio',
  },
  parameters: {
    docs: {
      description: {
        story: 'これはラジオボタンのデフォルトの状態です。',
      },
    },
  },
};

export const Disabled: StoryT = {
  args: {
    ...Default.args,
    disabled: true,
  },
  parameters: {
    docs: {
      description: {
        story: '操作できない無効なラジオボタン。',
      },
    },
  },
};

export const Checked: StoryT = {
  args: {
    ...Default.args,
    checked: true,
  },
  parameters: {
    docs: {
      description: {
        story: '事前に選択またはチェックされたラジオボタン。',
      },
    },
  },
};

export const RadioGroup: StoryT = {
  args: {
    type: 'group',
    options: [
      { label: 'A', value: 'A' },
      { label: 'B', value: 'B' },
      { label: 'C', value: 'C' },
      { label: 'D', value: 'D' },
    ],
    defaultValue: 'A',
  },
  parameters: {
    docs: {
      description: {
        story:
          'ラジオボタンのグループ。ユーザーはセットから1つのオプションを選択できます。',
      },
    },
  },
};

export const ControlledRadioGroup: StoryT = {
  render: () => {
    const [value, setValue] = useState('A');
    const options = [
      { label: 'A', value: 'A' },
      { label: 'B', value: 'B' },
      { label: 'C', value: 'C' },
      { label: 'D', value: 'D' },
    ];

    return (
      <ThemedRadio
        type="group"
        options={options}
        onChange={(e) => setValue(e.target.value)}
        value={value}
      />
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          '選択された値が親コンポーネントの状態によって管理される制御されたラジオグループ。',
      },
    },
  },
};

export const VerticalRadioGroup: StoryT = {
  args: {
    type: 'group',
    direction: 'vertical',
    options: [
      { label: 'A', value: 'A' },
      { label: 'B', value: 'B' },
      { label: 'C', value: 'C' },
      { label: 'D', value: 'D' },
    ],
    defaultValue: 'A',
  },
  parameters: {
    docs: {
      description: {
        story: 'オプションが垂直に表示されるラジオグループ。',
      },
    },
  },
};

export const RadioGroupWithDisabled: StoryT = {
  args: {
    type: 'group',
    options: [
      { label: 'A', value: 'A' },
      { label: 'B', value: 'B' },
      { label: 'C', value: 'C', disabled: true },
      { label: 'D', value: 'D' },
    ],
    defaultValue: 'A',
  },
  parameters: {
    docs: {
      description: {
        story: '選択できない無効なオプションを含むラジオグループ。',
      },
    },
  },
};

export const TabStyle: StoryT = {
  args: {
    type: 'tabs',
    defaultValue: 'c',
    options: [
      { label: 'Customer Search', value: 'a' },
      { label: 'Basic Information', value: 'b' },
      { label: 'Handle with Care', value: 'c' },
      { label: 'Customer Correspondence History', value: 'd' },
      { label: 'Document Management', value: 'e' },
    ],
  },
  parameters: {
    docs: {
      description: {
        story:
          'ラジオグループは、タブまたはソリッドボタンのようにスタイル設定できます。',
      },
    },
  },
};
