import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { Meta, StoryObj } from '@storybook/react-vite';
import { Tag } from './index';

const ThemedTag = withThemeProvider(Tag);

const meta: Meta<typeof Tag> = {
  title: 'Atoms/Tag',
  component: ThemedTag,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'タグは、アイテムにラベルを付けたり、分類したり、整理したりするために使用されます。これらは、メタデータやキーワードを表示するための柔軟で軽量な方法です。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Tag>;

export const Default: StoryT = {
  args: {
    label: 'Tag',
  },
  parameters: {
    docs: {
      description: {
        story:
          'デフォルトのタグは、アイテムを分類または整理するために使用できるシンプルなラベルです。',
      },
    },
  },
};

export const Closable: StoryT = {
  args: {
    ...Default.args,
    closable: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          '閉鎖可能なタグは、ユーザーが閉じることができ、アイテムのフィルタリングや削除に役立ちます。',
      },
    },
  },
};
