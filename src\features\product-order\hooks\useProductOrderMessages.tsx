import { CalendarOutlined } from '@ant-design/icons';
import { useTranslation } from 'next-i18next';
import { ProductOrderFormFieldKeyE } from '../enums';
import { ConveyMessageI } from '../types';

export const getProductOrderMessages = (
  t: ReturnType<typeof useTranslation>['t'],
) => {
  const orderConfirmationMessage: ConveyMessageI = {
    title: `【${t('numberOfOrders')}】 2${t('times')} （2025/02/18）`,
    buttonLabel: t('purchaseOrderSubmission'),
    descriptions: [
      {
        name: ProductOrderFormFieldKeyE.PMS_PRODUCT_NO,
        label: t('partNumber'),
      },
      {
        name: ProductOrderFormFieldKeyE.PMS_PRODUCT_NAME,
        label: t('productName'),
      },
    ],
  };

  const salesDepartmentMessage: ConveyMessageI = {
    title: t('salesDepartment'),
    buttonLabel: t('common:edit'),
    descriptions: [
      {
        height: 60,
        label: t('sellingPoint'),
        name: ProductOrderFormFieldKeyE.CONVEY_MSG,
      },
      {
        height: 60,
        label: t('otherNotes'),
        name: ProductOrderFormFieldKeyE.CONVEY_MSG,
      },
    ],
  };

  const processorMessage: ConveyMessageI = {
    title: t('processor'),
    buttonLabel: t('common:edit'),
    descriptions: [
      {
        height: 60,
        label: t('comment'),
        name: ProductOrderFormFieldKeyE.CONVEY_MSG,
      },
      {
        label: t('processingDate'),
        name: ProductOrderFormFieldKeyE.CONVEY_MSG,
        icon: <CalendarOutlined />,
      },
    ],
  };

  const inspectorMessage: ConveyMessageI = {
    title: t('inspector'),
    buttonLabel: t('common:edit'),
    descriptions: [
      {
        height: 60,
        label: t('comment'),
        name: ProductOrderFormFieldKeyE.CONVEY_MSG,
      },
    ],
  };

  return {
    orderConfirmationMessage,
    salesDepartmentMessage,
    processorMessage,
    inspectorMessage,
  };
};
