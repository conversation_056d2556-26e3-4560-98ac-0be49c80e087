import { ProductStoreBudgetI } from '@/features/products/types';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method === 'GET') {
    await new Promise((resolve) => setTimeout(resolve, 400));

    const data: ProductStoreBudgetI[] = [
      {
        id: 1,
        gender_category_name: 'MEN',
        gender_category_code: 'CODE028',
        store_budget_type_code: 'yy1',
        store_budget_type_name: 'ベーシック／体型カバー1',
      },
      {
        id: 2,
        gender_category_name: 'MEN',
        gender_category_code: 'CODE028',
        store_budget_type_code: 'yy0',
        store_budget_type_name: 'ベーシック／体型カバー5',
      },
      {
        id: 3,
        gender_category_name: 'WOWMAN',
        gender_category_code: 'CODE571',
        store_budget_type_code: 'yy2',
        store_budget_type_name: 'ベーシック／体型カバー2',
      },
      {
        id: 4,
        gender_category_name: 'KID(BOY)',
        gender_category_code: 'CODE572',
        store_budget_type_code: 'yy3',
        store_budget_type_name: 'ベーシック／体型カバー3',
      },
      {
        id: 5,
        gender_category_name: 'KID(BOY)',
        gender_category_code: 'CODE572',
        store_budget_type_code: 'yy4',
        store_budget_type_name: 'ベーシック／体型カバー4',
      },
    ];
    res.status(200).json({
      status: 200,
      result: {
        data,
      },
      message: 'データの取得に成功しました。',
    });
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
