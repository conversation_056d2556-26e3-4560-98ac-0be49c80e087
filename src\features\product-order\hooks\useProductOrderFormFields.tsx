import { FormFieldConfigI } from '@/components/organisms/Form';
import { ApiEndpointE } from '@/enums';
import { MESSAGES } from '@/enums/messages';
import { RegexE } from '@/enums/regex';
import { SelectE } from '@/enums/select';
import { SearchOutlined } from '@ant-design/icons';
import { TFunction } from 'i18next';
import { ColumnDefinition } from 'react-tabulator';
import { ProductOrderFormFieldKeyE } from '../enums';
import { ProductOrderDetailI } from '../types';

export const getProductOrderFormFields = (
  t: TFunction<readonly ['product-order', 'common'], undefined>,
  props: {
    setIsRetailPriceModalOpen: (open: boolean) => void;
    handleOpenSupplierFactoryModal: () => void;
    supplierFactories: ProductOrderDetailI['supplier_factories'];
    setSupplierFactories: (
      data: ProductOrderDetailI['supplier_factories'],
    ) => void;
    supplierFactoryColumns: ColumnDefinition[];
  },
): FormFieldConfigI[] => {
  const {
    setIsRetailPriceModalOpen,
    handleOpenSupplierFactoryModal,
    supplierFactories,
    setSupplierFactories,
    supplierFactoryColumns,
  } = props;

  return [
    {
      label: t('orderType'),
      name: ProductOrderFormFieldKeyE.PMS_ORDER_SBT_CODE,
      type: 'select',
      apiConfig: {
        params: { code_kbn: [SelectE.M_CODE_DROPDOWN_EXAMPLE], hidden_flg: 0 },
      },
      rules: [
        {
          required: true,
          message: MESSAGES.E_REQUIRED.replace('{attribute}', t('orderType')),
        },
      ],
      orderPosition: 0,
      requiredText: t('common:required'),
    },
    {
      label: t('orderDate'),
      name: ProductOrderFormFieldKeyE.PMS_ORDER_DATETIME,
      type: 'datepicker',
      rules: [
        {
          required: true,
          message: MESSAGES.E_REQUIRED.replace('{attribute}', t('orderDate')),
        },
      ],
      orderPosition: 2,
      requiredText: t('common:required'),
    },
    {
      name: ProductOrderFormFieldKeyE.PMS_JODAI_TAX_OUT_MODAL,
      label: t('retailPriceExclTaxYen'),
      type: 'field-group',
      fields: [
        {
          name: ProductOrderFormFieldKeyE.PMS_JODAI_TAX_OUT,
          type: 'input',
          readOnly: true,
          rules: [
            {
              required: true,
              message: MESSAGES.E_REQUIRED.replace(
                '{attribute}',
                t('retailPriceExclTaxYen'),
              ),
            },
          ],
        },
        {
          name: ProductOrderFormFieldKeyE.OPEN_PMS_JODAI_TAX_OUT_MODAL,
          type: 'button',
          labelButton: t('common:search'),
          onClick: () => setIsRetailPriceModalOpen(true),
          iconButton: <SearchOutlined />,
        },
      ],
      orderPosition: 4,
      requiredText: t('common:required'),
    },
    {
      label: t('retailPriceInclTaxYen'),
      name: ProductOrderFormFieldKeyE.PMS_JODAI_TAX_IN,
      type: 'input',
      orderPosition: 6,
      readOnly: true,
    },
    {
      label: t('calculationRate'),
      name: ProductOrderFormFieldKeyE.PMS_ORDER_CALC_RATE,
      type: 'input',
      rules: [
        {
          pattern: new RegExp(RegexE.ONLY_NUMBERS),
          message: MESSAGES.E_NUMERIC_ONLY.replace(
            '{attribute}',
            t('calculationRate'),
          ),
        },
        {
          max: 12,
          message: MESSAGES.E_MAXLENGTH.replace('{maxlength}', '12'),
        },
      ],
      orderPosition: 8,
    },
    {
      label: t('countryOfOrigin'),
      name: ProductOrderFormFieldKeyE.COUNTRY_ORIGIN_CODE,
      type: 'select',
      apiConfig: {
        params: { code_kbn: [SelectE.M_CODE_DROPDOWN_EXAMPLE], hidden_flg: 0 },
      },
      orderPosition: 1,
    },
    {
      label: t('importMethod'),
      name: ProductOrderFormFieldKeyE.IMPORT_METHOD_CODE,
      type: 'select',
      apiConfig: {
        params: { code_kbn: [SelectE.M_CODE_DROPDOWN_EXAMPLE], hidden_flg: 0 },
      },
      orderPosition: 3,
    },
    {
      label: t('importCostUnit'),
      name: ProductOrderFormFieldKeyE.IMPORT_COST_UNIT_CODE,
      type: 'select',
      apiConfig: {
        params: { code_kbn: [SelectE.M_CODE_DROPDOWN_EXAMPLE], hidden_flg: 0 },
      },
      orderPosition: 5,
    },
    {
      label: t('depositAmount'),
      name: ProductOrderFormFieldKeyE.PAYMENT_KGN,
      type: 'input',
      rules: [
        {
          pattern: new RegExp(RegexE.DECIMAL_NUMBER),
          message: MESSAGES.E_INVALID_DECIMAL_FORMAT.replace(
            '{integer}',
            '12',
          ).replace('{decimal}', '3'),
        },
        {
          max: 15,
          message: MESSAGES.E_MAXLENGTH.replace('{maxlength}', '15'),
        },
      ],
      orderPosition: 7,
    },
    {
      label: t('productionManager'),
      name: ProductOrderFormFieldKeyE.PMS_MANAGER_CODE,
      type: 'select',
      apiConfig: {
        params: { code_kbn: [SelectE.M_CODE_DROPDOWN_EXAMPLE], hidden_flg: 0 },
      },
      orderPosition: 9,
    },
    {
      label: t('common:supplier'),
      name: ProductOrderFormFieldKeyE.PMS_SUPPLIER_CODE,
      isFullRow: true,
      type: 'field-group',
      orderPosition: 10,
      fields: [
        {
          name: ProductOrderFormFieldKeyE.PMS_SUPPLIER_CODE,
          type: 'select',
          apiConfig: {
            endpoint: ApiEndpointE.SUPPLIER,
            params: { hidden_flg: 0 },
            mapFields: {
              label: SelectE.SUPPLIER_FIELD_NAME,
              value: SelectE.SUPPLIER_FIELD_CODE,
            },
          },
        },
        {
          name: ProductOrderFormFieldKeyE.OPEN_SUPPLIER_MODEL,
          type: 'button',
          labelButton: t('addASupplierFactory'),
          onClick: handleOpenSupplierFactoryModal,
        },
      ],
    },
    {
      name: ProductOrderFormFieldKeyE.PMS_SUPPLIER_FACTORY_CODE,
      isFullRow: true,
      type: 'editable-table',
      editableTableConfig: {
        data: supplierFactories || [],
        columns: supplierFactoryColumns,
        setData: setSupplierFactories,
      },
      orderPosition: 11,
    },
    {
      label: t('advanceCheck'),
      name: ProductOrderFormFieldKeyE.INSPECT_PRE_FLG,
      type: 'checkbox',
      orderPosition: 13,
    },
    {
      label: t('confirmedBy'),
      name: ProductOrderFormFieldKeyE.INSPECT_PRE_STAFF_CODE,
      type: 'select',
      apiConfig: {
        params: { code_kbn: [SelectE.M_CODE_DROPDOWN_EXAMPLE], hidden_flg: 0 },
      },
      orderPosition: 12,
    },
    {
      label: t('confirmationDateAndTime'),
      name: ProductOrderFormFieldKeyE.INSPECT_PRE_DATETIME,
      type: 'datepicker',
      showTime: true,
      orderPosition: 14,
    },
    {
      label: t('advanceNotes'),
      name: ProductOrderFormFieldKeyE.INSPECT_PRE_REMARKS,
      type: 'textarea',
      rows: 4,
      orderPosition: 15,
      isFullRow: true,
      rules: [
        {
          max: 10000,
          message: MESSAGES.E_MAXLENGTH.replace('{maxlength}', '10000'),
        },
      ],
    },
  ];
};
