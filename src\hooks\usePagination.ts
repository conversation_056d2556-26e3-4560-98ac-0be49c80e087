import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from '@/constants/common';
import { useState } from 'react';

const usePagination = () => {
  const [pagination, setPagination] = useState({
    current: DEFAULT_PAGE,
    pageSize: DEFAULT_PAGE_SIZE,
  });

  const handlePaginationChange = (current: number, pageSize: number) => {
    setPagination({ current, pageSize });
  };

  const setPage = (page: number) => {
    setPagination((prev) => ({ ...prev, current: page }));
  };

  const setDefaultPage = () => {
    setPagination((prev) => ({ ...prev, current: DEFAULT_PAGE }));
  };

  return {
    pagination: {
      ...pagination,
      onChange: handlePaginationChange,
    },
    setPage,
    setDefaultPage,
  };
};

export default usePagination;
