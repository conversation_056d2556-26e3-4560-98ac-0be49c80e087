/* Custom Pagination Styles */
.custom-pagination {
  .ant-pagination-prev {
    display: none !important;
  }

  .ant-pagination-next {
    display: none !important;
  }

  .ant-pagination-item {
    height: 24px !important;
    line-height: unset !important;
    border-radius: 100% !important;
    padding: unset !important;
    min-width: 24px !important;
    border: 1px solid #bbbbbb !important;

    a {
      padding: 0 !important;
    }

    &:hover {
      background-color: white !important;
      border-color: var(--button-accent-text) !important;

      a {
        color: var(--button-accent-text) !important;
      }
    }
  }

  .ant-pagination-item-ellipsis,
  .ant-pagination-item-container {
    line-height: 24px !important;
    height: 24px !important;
  }

  .ant-pagination-item-active {
    background-color: var(--button-accent-text) !important;
    border-color: var(--button-accent-text) !important;

    a {
      color: #fff !important;
    }
  }
}
