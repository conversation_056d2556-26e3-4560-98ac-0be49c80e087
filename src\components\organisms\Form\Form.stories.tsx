import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { Form, FormFieldConfigI } from '.';

const ThemedForm = withThemeProvider(Form, { padding: '30px' });

const meta: Meta<typeof Form> = {
  title: 'Organisms/Form',
  component: ThemedForm as any,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'マルチカラムレイアウトでさまざまなフィールドタイプをレンダリングできる、柔軟でカスタマイズ可能なフォームコンポーネント。高度な再利用性を実現するように設計されており、`formFields` プロップを介して構成できます。',
      },
    },
  },
  argTypes: {
    numOfColumns: {
      control: { type: 'number', min: 1, max: 4, step: 1 },
    },
  },
};

export default meta;
type StoryT = StoryObj<typeof meta>;

const formFields: FormFieldConfigI[] = [
  { label: '顧客番号', name: 'customerNo', type: 'input' },
  { label: 'FMC番号', name: 'fmcNo', type: 'input' },
  { label: '担当者名', name: 'contactPersonName', type: 'input' },
  { label: '顧客名', name: 'customerName', type: 'input' },
  { label: '顧客名(カナ)', name: 'customerNameKana', type: 'input' },
  { label: '住所', name: 'address', type: 'textarea' },
  { label: '代表者名', name: 'representativeName', type: 'input' },
  { label: '代表者名(カナ)', name: 'representativeNameKana', type: 'input' },
  { label: 'e-mail', name: 'email', type: 'input' },
  {
    label: 'ステータス',
    name: 'status',
    type: 'radiogroup',
    options: [
      { value: 'active', label: '有効' },
      { value: 'inactive', label: '無効' },
    ],
  },
  { label: '添付ファイル', name: 'attachment', type: 'upload' },
  { label: '契約日', name: 'contractDate', type: 'datepicker' },
  {
    label: '契約種別',
    name: 'contractType',
    type: 'select',
    options: [
      { value: 'new', label: '新規' },
      { value: 'renewal', label: '更新' },
    ],
  },
  { label: '有効フラグ', name: 'active', type: 'checkbox' },
];

export const Default: StoryT = {
  args: {
    formFields,
    numOfColumns: 1,
  },
  parameters: {
    docs: {
      description: {
        story: '単一カラムレイアウトでフォームを表示します。',
      },
    },
  },
};

export const TwoColumns: StoryT = {
  args: {
    formFields,
    numOfColumns: 2,
  },
  parameters: {
    docs: {
      description: {
        story: '2カラムレイアウトでフォームを表示し、よりコンパクトにします。',
      },
    },
  },
};

export const ThreeColumns: StoryT = {
  args: {
    formFields: formFields.map((field) => ({
      ...field,
      labelColSpan: 10,
    })),
    numOfColumns: 3,
  },
  parameters: {
    docs: {
      description: {
        story:
          '3カラムレイアウトでフォームを表示し、より広い画面に適しています。',
      },
    },
  },
};

export const CustomLabels: StoryT = {
  args: {
    formFields: [
      {
        label: 'Left Aligned',
        name: 'left',
        type: 'input',
        labelPosition: 'left',
        labelColSpan: 8,
      },
      {
        label: 'Center Aligned',
        name: 'center',
        type: 'input',
        labelPosition: 'center',
        labelColSpan: 8,
      },
      {
        label: 'Right Aligned',
        name: 'right',
        type: 'input',
        labelPosition: 'right',
        labelColSpan: 8,
      },
      {
        label: 'Label Span 8',
        name: 'span4',
        type: 'input',
        labelColSpan: 8,
      },
      {
        label: 'Label Span 8',
        name: 'span8',
        type: 'input',
        labelColSpan: 8,
      },
      {
        label: 'Label Span 8',
        name: 'span8',
        type: 'input',
        labelColSpan: 8,
      },
    ],
    numOfColumns: 2,
  },
  parameters: {
    docs: {
      description: {
        story:
          '個々のフィールドのラベルの配置と列スパンをカスタマイズする方法を示します。',
      },
    },
  },
};

export const WithValidation: StoryT = {
  args: {
    formFields: [
      {
        label: 'Username',
        name: 'username',
        type: 'input',
        rules: [{ required: true, message: 'Please input your username!' }],
      },
      {
        label: 'Password',
        name: 'password',
        type: 'password',
        rules: [{ required: true, message: 'Please input your password!' }],
      },
    ],
    numOfColumns: 1,
    onFinish: (values) => alert(`Success: ${JSON.stringify(values)}`),
    onFinishFailed: (errorInfo) =>
      alert(`Failed: ${JSON.stringify(errorInfo)}`),
  },
  render: ((args: any) => <ThemedForm {...args} />) as any,
  parameters: {
    docs: {
      description: {
        story:
          '検証ルール付きのフォームを示します。フィールドが入力されていない場合、フォームはエラーメッセージを表示します。',
      },
    },
  },
};

const validationFormFields: FormFieldConfigI[] = [
  {
    name: 'email',
    label: 'Email',
    type: 'input',
    rules: [
      { required: true, message: 'Please input your email!' },
      { type: 'email', message: 'The input is not valid E-mail!' },
    ],
  },
  {
    name: 'number',
    label: 'Number',
    type: 'input',
    rules: [
      { required: true, message: 'Please input a number!' },
      { pattern: /^\d+$/, message: 'Please enter a valid number.' },
    ],
  },
  {
    name: 'gender',
    label: 'Gender',
    type: 'select',
    options: [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' },
    ],
    rules: [{ required: true, message: 'Please select your gender!' }],
  },
  {
    name: 'dob',
    label: 'Date of Birth',
    type: 'datepicker',
    rules: [{ required: true, message: 'Please select your date of birth!' }],
  },
  {
    name: 'agreement',
    label: 'Agreement',
    type: 'checkbox',
    rules: [
      {
        validator: (_, value) =>
          value
            ? Promise.resolve()
            : Promise.reject(new Error('You must accept the agreement')),
      },
    ],
  },
  {
    name: 'status',
    label: 'Status',
    type: 'radiogroup',
    options: [
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' },
    ],
    rules: [{ required: true, message: 'Please select a status!' }],
  },
  {
    name: 'comment',
    label: 'Comment',
    type: 'textarea',
    rules: [{ required: true, message: 'Please leave a comment!' }],
  },
];

export const WithComprehensiveValidation: StoryT = {
  args: {
    formFields: validationFormFields,
    numOfColumns: 2,
    onFinish: (values) => alert(`Success: ${JSON.stringify(values)}`),
    onFinishFailed: (errorInfo) =>
      alert(`Failed: ${JSON.stringify(errorInfo)}`),
  },
  render: ((args: any) => <ThemedForm {...args} />) as any,
  parameters: {
    docs: {
      description: {
        story:
          'Demonstrates validation for a comprehensive set of field types.',
      },
    },
  },
};
