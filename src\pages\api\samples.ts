import { <PERSON><PERSON><PERSON> } from '@/features/sample-information/types';
import { faker } from '@faker-js/faker';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method === 'GET') {
    const { page = 1, per_page = 10 } = req.query;

    await new Promise((resolve) => setTimeout(resolve, 400));

    const samples: SampleI[] = Array.from(
      { length: 50 },
      (_, i) =>
        ({
          id: i + 1,
          pms_sample_no:
            faker.string.alphanumeric(3) + '-' + faker.string.numeric(3),
          order_flg: faker.datatype.boolean() ? 1 : 0,
          receipt_flg: faker.datatype.boolean() ? 1 : 0,
          payment_flg: faker.datatype.boolean() ? 1 : 0,
          gender_category_code: faker.helpers.arrayElement([
            'M',
            'W',
            'KIDS(BOYS)',
            'KIDS(GIRLS)',
          ]),
          product_category_code: faker.commerce.productMaterial(),
          pms_sample_request_datetime: faker.date.past().toISOString(),
          pms_plan_datetime: faker.date.future().toISOString(),
          supplier_factory_code: faker.company.name(),
          supplier_code: faker.string.alphanumeric(5),
          pms_planner_code: faker.person.fullName(),
          pms_sample_remarks: faker.lorem.sentence(),
          updated_at: faker.date.recent().toISOString(),
          sample_media: {
            id: i + 1,
            pms_sample_media_kbn: faker.string.alphanumeric(2),
            pms_sample_media_file_seq_no: faker.number.int({ min: 1, max: 10 }),
            pms_sample_no:
              faker.string.alphanumeric(3) + '-' + faker.string.numeric(3),
            media_file: {
              media_file_kbn: faker.string.alphanumeric(2),
              media_file_logical_name: faker.system.fileName(),
              media_file_physical_name: faker.system.fileName(),
              media_file_seq_no: faker.number.int({ min: 1, max: 10 }),
              sort_key: faker.number.int({ min: 1, max: 100 }),
              media_file_path: faker.image.url(),
            },
          },
        }) as unknown as SampleI,
    );

    const start = (Number(page) - 1) * Number(per_page);
    const end = start + Number(per_page);
    const paginatedSamples = samples.slice(start, end);

    res.status(200).json({
      data: paginatedSamples,
      total: samples.length,
      page: Number(page),
      per_page: Number(per_page),
    });
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
