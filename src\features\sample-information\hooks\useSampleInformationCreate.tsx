import { Checkbox } from '@/components/atoms';
import { FormFieldConfigI } from '@/components/organisms/Form';
import { ApiEndpointE, RegexE } from '@/enums';
import { SelectE } from '@/enums/select';
import { useTranslation } from 'next-i18next';
import { useCallback, useState } from 'react';
import ReactDOM from 'react-dom/client';
import { ColumnDefinition } from 'react-tabulator';
import { SampleFormFieldKeyE } from '../enums';
import { createSample } from '../services';

export const useSampleCreateInformation = () => {
  const { t } = useTranslation('sample-information');

  const handleSubmit = async (basicForm: any, partnerForm: any) => {
    const basicValues = await basicForm.validateFields();
    const partnerValues = await partnerForm.validateFields();
    const sampleParams = { ...basicValues, ...partnerValues };

    //handle logic create, update here
    const result = await createSample(sampleParams);

    return { basicValues, partnerValues, result };
  };

  const handleCollapseAll = (setActiveKeys: (keys: string[]) => void) => {
    setActiveKeys(['partner', 'material', 'sampleImage', 'patternFiles']);
  };

  const handleCollapseNone = (setActiveKeys: (keys: string[]) => void) => {
    setActiveKeys([]);
  };

  const handleDelete = useCallback(async () => {
    // Implement delete logic here
  }, []);

  const [dataTableMateria, setDataTableMateria] = useState([
    {
      id: 1,
      name: '襟',
      delete: false,
      fabricUsedAreas: '生地使用箇所',
      fabricManagementNumberReviewMeeting: '生地管理番号検討会',
      fabricName: '生地名',
    },
    {
      id: 2,
      name: '襟',
      delete: false,
      fabricUsedAreas: '生地使用箇所',
      fabricManagementNumberReviewMeeting: '生地管理番号検討会',
      fabricName: '生地名',
    },
    {
      id: 3,
      name: '襟',
      delete: false,
      fabricUsedAreas: '生地使用箇所',
      fabricManagementNumberReviewMeeting: '生地管理番号検討会',
      fabricName: '生地名',
    },
  ]);

  const columnsMaterial: ColumnDefinition[] = [
    { title: 'No', field: 'id', width: 50 },
    {
      title: t('delete'),
      field: 'delete',
      formatter: (cell: any) => {
        const record = cell.getRow().getData();
        const el = document.createElement('div');
        const value = cell.getValue();
        const root = ReactDOM.createRoot(el);
        root.render(
          <Checkbox
            defaultChecked={value}
            onChange={(e) =>
              handleCheckboxChange(record.id, 'delete', e.target.checked)
            }
          />,
        );

        return el;
      },
      cellClick: (e: any, cell: any) => {
        if (e.target.classList.contains('receipt-checkbox')) {
          const record = cell.getRow().getData();
          handleCheckboxChange(record.id, 'delete', e.target.checked);
        }
      },
    },
    { title: t('fabricUsedAreas'), field: 'fabricUsedAreas', editor: 'input' },
    {
      title: t('fabricManagementNumberReviewMeeting'),
      field: 'fabricManagementNumberReviewMeeting',
    },
    { title: t('fabricName'), field: 'fabricName' },
  ];

  const basicFormFields: FormFieldConfigI[] = [
    {
      label: t('sampleNumber'),
      name: SampleFormFieldKeyE.SAMPLE_NUMBER,
      type: 'input',
      disabled: true,
    },
    {
      label: t('plainingDate'),
      name: SampleFormFieldKeyE.PLANNING_DATE,
      type: 'datepicker',
      rules: [{ required: true, message: t('pleaseEnterRequiredFields') }],
    },
    {
      label: t('planner'),
      name: SampleFormFieldKeyE.PLANNER,
      type: 'select',
      apiConfig: {
        endpoint: ApiEndpointE.MASTER_CODE,
        params: { code_kbn: [SelectE.M_CODE_BUYER], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      rules: [{ required: true, message: t('pleaseEnterRequiredFields') }],
    },
    {
      label: t('gender'),
      name: SampleFormFieldKeyE.GENDER_CATEGORY,
      type: 'select',
      apiConfig: {
        endpoint: ApiEndpointE.MASTER_CODE,
        params: { code_kbn: [SelectE.M_CODE_GENDERCATEGORYCD], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      rules: [{ required: true, message: t('pleaseEnterRequiredFields') }],
    },
    {
      label: t('category'),
      name: SampleFormFieldKeyE.PRODUCT_CATEGORY,
      type: 'input',
      rules: [{ required: true, message: t('pleaseEnterRequiredFields') }],
    },
    {
      label: t('originalLink'),
      name: SampleFormFieldKeyE.REFERENCE_URL,
      type: 'input',
      rules: [{ max: 200, message: t('maxLength', { max: 200 }) }],
    },
    {
      label: t('remarks'),
      name: SampleFormFieldKeyE.PREPARATION,
      type: 'textarea',
      rows: 4,
      rules: [{ max: 10000, message: t('maxLength', { max: 10000 }) }],
    },
  ];

  const partnerFormFields: FormFieldConfigI[] = [
    {
      label: t('sampleRequestDate'),
      name: SampleFormFieldKeyE.REQUEST_DATE,
      type: 'datepicker',
    },
    {
      label: t('partner'),
      name: SampleFormFieldKeyE.PARTNER_FILTER,
      type: 'field-group',
      fields: [
        {
          name: SampleFormFieldKeyE.PARTNER,
          type: 'select',
          apiConfig: {
            endpoint: ApiEndpointE.SUPPLIER,
            params: { hidden_flg: 0 },
            mapFields: {
              label: SelectE.SUPPLIER_FIELD_NAME,
              value: SelectE.SUPPLIER_FIELD_CODE,
            },
          },
        },
        {
          name: SampleFormFieldKeyE.OPEN_PARTNER_MODEL,
          type: 'button',
          labelButton: t('addAPartnerFactory'),
        },
      ],
    },
    {
      name: SampleFormFieldKeyE.PARTNER_FACTORY,
      type: 'editable-table',
      editableTableConfig: {
        data: dataTableMateria,
        columns: columnsMaterial,
        setData: setDataTableMateria,
      },
    },
    {
      label: t('estimatedPrice'),
      name: SampleFormFieldKeyE.ESTIMATE_PRICE_AND_CURRENCY,
      type: 'field-group',
      fields: [
        {
          name: SampleFormFieldKeyE.QUOTE_KGN,
          type: 'input',
          rules: [
            {
              pattern: new RegExp(RegexE.DECIMAL_NUMBER),
              message: t('priceIntegerDecimalLimit', {
                integer: 10,
                decimal: 2,
              }),
            },
          ],
        },
        {
          name: SampleFormFieldKeyE.QUOTE_KGN_UNIT_CODE,
          type: 'select',
          apiConfig: {
            endpoint: ApiEndpointE.MASTER_CODE,
            params: { code_kbn: [SelectE.M_CODE_GAIKA], hidden_flg: 0 },
            mapFields: {
              label: SelectE.M_CODE_FIELD_CODE_NAME,
              value: SelectE.M_CODE_FIELD_CODE,
            },
          },
        },
      ],
    },
    {
      label: t('creationCount'),
      name: SampleFormFieldKeyE.PMS_SAMPLE_CREATE_CNT,
      type: 'input',
    },
    { label: t('order'), name: SampleFormFieldKeyE.ORDER, type: 'checkbox' },
    {
      label: t('receipt'),
      name: SampleFormFieldKeyE.RECEIPT,
      type: 'checkbox',
    },
    {
      label: t('payment'),
      name: SampleFormFieldKeyE.PAYMENT,
      type: 'checkbox',
    },
  ];

  const handleCheckboxChange = (
    id: number,
    field: string,
    checked: boolean,
  ) => {
    setDataTableMateria((prevData) =>
      prevData.map((item) =>
        item.id === id ? { ...item, [field]: checked } : item,
      ),
    );
  };

  return {
    basicFormFields,
    partnerFormFields,
    handleSubmit,
    handleCollapseAll,
    handleCollapseNone,
    handleDelete,
    dataTableMateria,
    columnsMaterial,
    setDataTableMateria,
  };
};
