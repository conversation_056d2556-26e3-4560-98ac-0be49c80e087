import { Button, Table } from '@/components/atoms';
import { useTranslation } from 'react-i18next';
import useOrderInformationList from '../../hooks/useOrderInformationList';
import { OrderInformationDataTableI, ProductDetailI } from '../../types';
import ViewTable from '../ColorList/ViewTable';

interface OrderInformationListPropsI {
  product?: ProductDetailI;
}

const OrderInformationList = ({ product }: OrderInformationListPropsI) => {
  const { t } = useTranslation('products');
  const { pagination, columns, isLoading, data, total, isFetching } =
    useOrderInformationList();

  return (
    <div className="flex w-full flex-col gap-4">
      <ViewTable
        data={[
          {
            label: t('partNumber'),
            value: `${product?.pms_product_no || ''}`,
          },
          {
            label: t('productName'),
            value: `${product?.pms_remarks || ''}`,
          },
        ]}
      />
      <div className="flex w-full items-center gap-2">
        <Button
          label={t('purchaseOrderSubmission')}
          color="primary"
          variant="outlined"
        />
        <Button
          label={t('addOrderingInformation')}
          color="primary"
          variant="outlined"
        />
      </div>
      <Table<OrderInformationDataTableI>
        columns={columns}
        dataSource={data}
        rowKey="id"
        loadingMinHeight="160px"
        loading={isLoading || isFetching}
        pagination={{
          ...pagination,
          total,
        }}
      ></Table>
    </div>
  );
};

export default OrderInformationList;
