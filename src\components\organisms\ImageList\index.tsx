import { Loading, Upload } from '@/components/atoms';
import { IMAGE_TYPES, MAX_IMAGE_SIZE } from '@/constants/common';
import { MESSAGES } from '@/enums/messages';
import useLocalization from '@/hooks/useLocalization';
import { uploadFiles } from '@/services/common';
import { UploadFileResponseI } from '@/types/api';
import { FileUtil } from '@/utils';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Card, message } from 'antd';
import { UploadChangeParam } from 'antd/es/upload';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import styled from 'styled-components';
import { DraggableImageCard, ImageItemI } from './DraggableImageCard';
import { UploadButton } from './UploadButton';

interface ImageUploadListPropsI {
  onUploadSuccess?: (images: UploadFileResponseI[]) => void;
  onUploadError?: (error: Error) => void;
}

const UploadDragger = styled(Upload.Dragger)`
  .ant-upload .ant-upload-btn {
    padding: 0 !important;
  }
`;

const ImageUploadList: React.FC<ImageUploadListPropsI> = (props) => {
  const { onUploadSuccess, onUploadError } = props;

  const [images, setImages] = useState<ImageItemI[]>([]);
  const [checkedIds, setCheckedIds] = useState<string[]>([]);
  const [selectedImageId, setSelectedImageId] = useState<string | null>(null);
  const { tComponents } = useLocalization();
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const isDraggingCardRef = useRef<boolean>(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);

  const selectedImageIndex = useMemo(
    () => images.findIndex((img) => img.uid === selectedImageId),
    [images, selectedImageId],
  );

  const scrollToBottom = () => {
    const container = scrollContainerRef.current;

    if (container) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  const handleUpload =
    (type: 'upload' | 'drop') => async (info: UploadChangeParam) => {
      try {
        const { file } = info;

        let fileList = info.fileList;

        fileList =
          type === 'upload'
            ? fileList?.filter(
                (item) => !images.some((img) => img.uid === item.uid),
              )
            : fileList;

        const allFinished = fileList.every(
          (f) => f.status === 'done' || f.status === 'error',
        );

        if (allFinished) {
          setIsUploading(true);
          if (!IMAGE_TYPES.includes(file?.originFileObj?.type || '')) {
            message.error(MESSAGES.E_MIMES_IMAGE);

            return;
          }

          if (Number(file?.originFileObj?.size) > MAX_IMAGE_SIZE) {
            message.error(MESSAGES.E_MAX_FILE_SIZE);

            return;
          }

          const newImages = await Promise.all(
            fileList.map(async (item) => ({
              url: await FileUtil.fileToBase64(item.originFileObj as File),
              file: item.originFileObj as File,
              uid: item.uid,
            })),
          );

          setImages((prevImages) => [...prevImages, ...newImages]);

          if (images.length === 0) {
            setSelectedImageId(newImages?.[0]?.uid);
          }

          const responseFiles = await uploadFiles(
            fileList?.map((item) => item.originFileObj as File),
            'image',
          );

          onUploadSuccess?.(responseFiles);
        }
      } catch (error) {
        onUploadError?.(error as Error);
      } finally {
        setIsUploading(false);
      }
    };

  const handleCheckChange = (id: string) => {
    const newCheckedIds = checkedIds.includes(id)
      ? checkedIds.filter((i) => i !== id)
      : [...checkedIds, id];
    setCheckedIds(newCheckedIds);
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    const updatedImages = [...images];
    const [movedImage] = updatedImages.splice(fromIndex, 1);
    updatedImages.splice(toIndex, 0, movedImage);
    setImages(updatedImages);

    setCheckedIds((prevCheckedIds) => {
      return prevCheckedIds.map((id) => {
        if (id === images[fromIndex].uid) {
          return updatedImages[toIndex].uid;
        }

        return id;
      });
    });

    if (selectedImageId === images[fromIndex].uid) {
      setSelectedImageId(updatedImages[toIndex].uid);
    }
  };

  const handleImageClick = (id: string) => {
    setSelectedImageId(id);
  };

  const handleRemoveSelected = () => {
    const newImages = images.filter((img) => !checkedIds.includes(img.uid));
    images
      .filter((img) => checkedIds.includes(img.uid))
      .forEach((image) => URL.revokeObjectURL(image.url));
    setImages(newImages);
    setCheckedIds([]);
    setSelectedImageId(newImages.length > 0 ? newImages[0].uid : null);
  };

  useEffect(() => {
    scrollToBottom();
  }, [images.length]);

  const DropArea = ({ UploadButton }: { UploadButton: React.ReactNode }) => {
    return (
      <UploadDragger
        accept="image/*"
        showUploadList={false}
        customRequest={FileUtil.dummyRequest}
        style={{ width: '100%', padding: 0 }}
        multiple
        onChange={handleUpload('drop')}
      >
        <Card
          hoverable
          style={{
            width: '100%',
            height: '100%',
            textAlign: 'center',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
          }}
          bodyStyle={{ padding: 0 }}
          onDrop={() => {
            setTimeout(() => {
              setIsDragging(false);
            }, 50);
          }}
        >
          <div className="drop-area absolute top-0 left-0 h-full w-full" />
          {UploadButton}
        </Card>
      </UploadDragger>
    );
  };

  return (
    <Loading spinning={isUploading}>
      <div>
        <div
          style={{
            borderRadius: 8,
            position: isDragging ? 'relative' : 'unset',
          }}
        >
          {images.length === 0 && (
            <DropArea
              UploadButton={
                <UploadButton
                  onDragLeave={() => {
                    setIsDragging(false);
                  }}
                />
              }
            />
          )}

          <div
            ref={scrollContainerRef}
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
              gridAutoRows: 'minmax(150px, auto)',
              gap: 10,
              marginTop: 16,
              maxHeight: 478,
              overflow: 'auto',

              position: isDragging ? 'unset' : 'relative',
            }}
            className="custom-scrollbar"
            onDragEnter={() => {
              if (isDraggingCardRef.current) {
                return;
              }

              setIsDragging(true);
            }}
          >
            <div
              style={{
                zIndex: isDragging ? 1000 : 0,
              }}
              className="absolute top-0 left-0 z-1000 h-full w-full"
            >
              {isDragging && images.length > 0 && (
                <DropArea
                  UploadButton={
                    <UploadButton
                      onDragLeave={() => {
                        setIsDragging(false);
                      }}
                    />
                  }
                />
              )}
            </div>

            {images.length > 0 && selectedImageIndex >= 0 && (
              <div
                style={{ gridRow: 'span 2', gridColumn: 'span 2' }}
                className="overflow-hidden rounded-md border-1 border-[var(--border-color)] bg-white"
              >
                <img
                  alt="main"
                  src={images[selectedImageIndex].url}
                  style={{
                    height: '100%',
                    objectFit: 'contain',
                    width: '100%',
                    maxHeight: 310,
                  }}
                />
              </div>
            )}

            <DndProvider backend={HTML5Backend}>
              {images.map((image, index) => (
                <DraggableImageCard
                  key={image.uid}
                  index={index}
                  image={image}
                  moveImage={moveImage}
                  isChecked={checkedIds.includes(image.uid)}
                  onCheckChange={handleCheckChange}
                  onClickCard={handleImageClick}
                  onDragStart={() => {
                    isDraggingCardRef.current = true;
                  }}
                  onDragEnd={() => {
                    isDraggingCardRef.current = false;
                  }}
                />
              ))}
            </DndProvider>

            {images.length > 0 && (
              <Upload
                customRequest={FileUtil.dummyRequest}
                onChange={handleUpload('upload')}
                showUploadList={false}
                accept="image/*"
                multiple
                style={{
                  width: '100%',
                  height: '100%',
                }}
              >
                <Button className="flex min-h-[150px] w-full items-center justify-center rounded-[4px] border !border-dashed border-[var(--border-color)]">
                  <PlusOutlined
                    style={{
                      fontSize: 24,
                      color: 'var(--text-color-secondary)',
                    }}
                  />
                </Button>
              </Upload>
            )}
          </div>

          {checkedIds.length > 0 && (
            <div style={{ marginTop: 16 }}>
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
                onClick={handleRemoveSelected}
              >
                {tComponents('form.deleteImage')}
              </Button>
            </div>
          )}
        </div>
      </div>
    </Loading>
  );
};

export default ImageUploadList;
