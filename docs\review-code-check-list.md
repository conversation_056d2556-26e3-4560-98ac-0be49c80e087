### Next.js Code Review Checklist

#### 1. Project Structure

- [ ] Is the directory structure well organized (e.g., `pages`, `components`, `lib`, `styles` are properly separated)?
- [ ] Are there no unnecessary files or folders?
- [ ] Are file naming conventions consistent (e.g., components use PascalCase, utility functions use camelCase)?
- [ ] Are there comments for file headers and function headers?

#### 2. Next.js Specific Features

- [ ] Are `getStaticProps` and `getServerSideProps` used appropriately (is the correct data fetching method chosen)?
- [ ] Is `getServerSideProps` not used unnecessarily (considering performance)?
- [ ] Is `getStaticPaths` implemented correctly (are fallback settings appropriate in dynamic routing)?
- [ ] Are an excessive number of paths not being generated?
- [ ] Is the design of API Routes appropriate (are endpoints within `pages/api` well organized)?
- [ ] Is overly complex logic avoided?

#### 3. Component Design

- [ ] Are components reusable (is there no duplicate code across different parts)?
- [ ] Are props used appropriately?
- [ ] Is state management appropriate (is `useState` or `useReducer` not used excessively)?
- [ ] If global state is needed, are Context, Redux, or Zustand used?
- [ ] Is excessive logic not written inside components (i.e., avoiding Fat Components)?
- [ ] Is there no code that causes unnecessary re-renders?

#### 4. Performance

- [ ] Are images optimized (is `next/image` being used)?
- [ ] Are images served in appropriate sizes and formats?
- [ ] Is code splitting appropriate (is no unnecessarily large bundle being created)?
- [ ] Is `dynamic()` used for lazy loading components?
- [ ] Are unnecessary re-renders avoided (is `React.memo`, `useCallback`, and `useMemo` used correctly)?

#### 5. Security

- [ ] Are XSS protections in place (is user input not directly inserted into the DOM)?
- [ ] If `dangerouslySetInnerHTML` is used, is the content properly sanitized?
- [ ] Is API error handling appropriate (are errors caught and are users provided with appropriate feedback)?
- [ ] Is environment variable management appropriate (is there no sensitive data leak via `process.env`)?
- [ ] Is the `.env` file included in `.gitignore`?

#### 6. Styling & Accessibility

- [ ] Is CSS management appropriate (is a consistent styling method like styled-components, CSS Modules, or Tailwind CSS used)?
- [ ] Is global CSS not overused?
- [ ] Is accessibility considered (are `aria-*` attributes used appropriately)?
- [ ] Is keyboard navigation supported?
- [ ] Is there sufficient color contrast?

#### 7. Testing

- [ ] Are tests implemented (are unit and integration tests properly written)?
- [ ] Are tools like Jest or React Testing Library used?
- [ ] Are edge cases considered (are abnormal behaviors tested)?

#### 8. Other Best Practices

- [ ] Are TypeScript types defined appropriately (are types accurately defined)?
- [ ] Is `any` not overused?
- [ ] Are ESLint and Prettier configured (is code style consistent)?
- [ ] Are there no unnecessary warnings or errors?
- [ ] Are dependencies up to date (are dependencies in `package.json` using the latest versions)?
- [ ] Are there no unnecessary dependencies included?

#### 9. Common Anti-Patterns

- [ ] Is `useEffect` used correctly (no incorrect dependency arrays or infinite loops)?
- [ ] Are there no leftover `console.log` statements (no debug code left)?
- [ ] Is hardcoding avoided (no URLs or config values written directly in the code)?

---
