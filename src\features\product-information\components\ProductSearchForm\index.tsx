'use client';

import { But<PERSON>, Tag } from '@/components/atoms';
import { DateRange } from '@/components/atoms/DateRange';
import { Form, FormFieldConfigI } from '@/components/organisms/Form';
import { ApiEndpointE } from '@/enums';
import { SelectE } from '@/enums/select';
import { ProductFormFieldKeyE } from '@/features/product-information/enums';
import type { ProductSearchFormValuesI } from '@/features/product-information/types';
import { ProductClassificationDataI } from '@/features/product-information/types';
import { useResponsive } from '@/hooks';
import { DateHelper } from '@/utils';
import {
  ClearOutlined,
  CloseOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { Form as AntdForm, message } from 'antd';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { ProductClassificationModal } from '../ProductClassificationModal';

type ProductSearchFormPropsT = {
  onSearch?: (values: ProductSearchFormValuesI) => void;
};

export default function ProductSearchForm({
  onSearch,
}: ProductSearchFormPropsT) {
  const { t } = useTranslation('products');
  const [form] = AntdForm.useForm();

  const [productClassificationValues, setProductClassificationValues] =
    useState<{ code: string; label: string }[]>([]);

  const [productClassificationItems, setProductClassificationItems] = useState<
    ProductClassificationDataI[]
  >([]);

  const triggerSearch = async () => {
    try {
      const values = await form.validateFields();
      onSearch?.(values);
    } catch {
      message.error(t('formSearchError'));
    }
  };

  const handleClear = () => {
    form.resetFields();
    setProductClassificationValues([]);
    setProductClassificationItems([]);
    onSearch?.({});
  };

  const validateDateRange = (fromFieldName: string, toFieldName: string) => {
    return () => ({
      validator() {
        const formValues = form.getFieldsValue();
        const fromDate = formValues[fromFieldName];
        const toDate = formValues[toFieldName];

        if (fromDate && toDate && fromDate > toDate) {
          return Promise.reject(
            new Error(t('startDateMustBeLessOrEqualEndDate')),
          );
        }

        return Promise.resolve();
      },
    });
  };

  // Product category selection modal
  const openProductClassificationModal = () => {
    ProductClassificationModal.open({
      title: t('productClassificationSelection'),
      initialSelectedValues: productClassificationItems,
      onSelect: (selectedValues, selectedItems) => {
        const selectedValuesString = selectedValues.join(', ');
        form.setFieldsValue({
          [ProductFormFieldKeyE.PRODUCT_TYPE]: selectedValuesString,
        });

        // Update with array of objects for tag display
        const tagItems =
          selectedItems?.map((item) => ({
            code: item.code,
            label: `${item.classification} (${item.code})`,
          })) ||
          selectedValues.map((value) => ({
            code: value,
            label: value,
          }));

        // Replace the entire selection instead of adding to it
        setProductClassificationValues(tagItems);

        // Also store the complete items for future modal opens
        if (selectedItems) {
          setProductClassificationItems(selectedItems);
        }
      },
    });
  };

  const formFields: FormFieldConfigI[] = [
    {
      label: t('productNo'),
      name: ProductFormFieldKeyE.PRODUCT_NO,
      type: 'textarea',
      extra: <p style={{ marginTop: '10px' }}>{t('multipleInputHint')}</p>,
      placeholder: '26-240203003-10\n46-240203003-04',
      rows: 3,
      orderPosition: 0,
    },
    {
      label: '',
      name: '',
      type: 'empty',
    },
    {
      label: t('productNameSearch'),
      name: ProductFormFieldKeyE.PRODUCT_NAME,
      type: 'input',
      orderPosition: 1,
    },
    {
      label: t('fabricNo'),
      name: ProductFormFieldKeyE.FABRIC_NO,
      type: 'input',
      orderPosition: 3,
    },
    {
      label: t('planner'),
      name: ProductFormFieldKeyE.PLANNER,
      type: 'select',
      mode: 'multiple',
      apiConfig: {
        endpoint: ApiEndpointE.MASTER_CODE,
        params: { code_kbn: [SelectE.M_CODE_BUYER], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      orderPosition: 2,
    },
    {
      label: t('fabricNameSearch'),
      name: ProductFormFieldKeyE.FABRIC_NAME,
      type: 'input',
      orderPosition: 4,
    },
    {
      label: t('productionCategory'),
      name: ProductFormFieldKeyE.PRODUCT_CATEGORY,
      type: 'select',
      mode: 'multiple',
      apiConfig: {
        endpoint: ApiEndpointE.MASTER_CODE,
        params: { code_kbn: [SelectE.M_CODE_TMPCATECN], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      orderPosition: 5,
    },
    {
      label: t('color'),
      name: ProductFormFieldKeyE.COLOR,
      type: 'select',
      mode: 'multiple',
      apiConfig: {
        endpoint: ApiEndpointE.MASTER_CODE,
        params: { code_kbn: [SelectE.M_CODE_COLOR], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      orderPosition: 7,
    },
    {
      label: t('genderCategory'),
      name: ProductFormFieldKeyE.GENDER_CATEGORY,
      type: 'select',
      mode: 'multiple',
      apiConfig: {
        endpoint: ApiEndpointE.MASTER_CODE,
        params: { code_kbn: [SelectE.M_CODE_GENDERCATEGORYCD], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      orderPosition: 6,
    },
    {
      label: t('size'),
      name: ProductFormFieldKeyE.SIZE,
      type: 'select',
      mode: 'multiple',
      apiConfig: {
        endpoint: ApiEndpointE.MASTER_CODE,
        params: { code_kbn: [SelectE.M_CODE_SIZE], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      orderPosition: 8,
    },
    {
      label: t('partner'),
      name: ProductFormFieldKeyE.PARTNER,
      type: 'select',
      apiConfig: {
        endpoint: ApiEndpointE.SUPPLIER,
        params: { hidden_flg: 0 },
        mapFields: {
          label: SelectE.SUPPLIER_FIELD_NAME,
          value: SelectE.SUPPLIER_FIELD_CODE,
        },
      },
      orderPosition: 9,
    },
    {
      label: t('orderType'),
      name: ProductFormFieldKeyE.ORDER_TYPE,
      type: 'select',
      mode: 'multiple',
      apiConfig: {
        endpoint: ApiEndpointE.MASTER_CODE,
        params: { code_kbn: [SelectE.M_CODE_ORDER_TYPE], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      orderPosition: 10,
    },
    {
      label: t('deliveryPeriodPlanned'),
      name: ProductFormFieldKeyE.LOCAL_SHIPPING_PLAN_DATETIME,
      type: 'datepicker-range',
      rules: [
        validateDateRange(
          ProductFormFieldKeyE.LOCAL_SHIPPING_PLAN_DATETIME_FROM,
          ProductFormFieldKeyE.LOCAL_SHIPPING_PLAN_DATETIME_TO,
        ),
      ],
      render: () => (
        <DateRange
          fromName={ProductFormFieldKeyE.LOCAL_SHIPPING_PLAN_DATETIME_FROM}
          toName={ProductFormFieldKeyE.LOCAL_SHIPPING_PLAN_DATETIME_TO}
          placeholder={[t('startDate'), t('endDate')]}
          format={DateHelper.FORMAT.FORMAT_2}
        />
      ),
      orderPosition: 11,
    },
    {
      label: t('storeArrivalPlanDatetime'),
      name: ProductFormFieldKeyE.STORE_ARRIVAL_PLAN_DATETIME,
      type: 'datepicker-range',
      rules: [
        validateDateRange(
          ProductFormFieldKeyE.STORE_ARRIVAL_PLAN_DATETIME_FROM,
          ProductFormFieldKeyE.STORE_ARRIVAL_PLAN_DATETIME_TO,
        ),
      ],
      render: () => (
        <DateRange
          fromName={ProductFormFieldKeyE.STORE_ARRIVAL_PLAN_DATETIME_FROM}
          toName={ProductFormFieldKeyE.STORE_ARRIVAL_PLAN_DATETIME_TO}
          placeholder={[t('startDate'), t('endDate')]}
          format={DateHelper.FORMAT.FORMAT_2}
        />
      ),
      orderPosition: 13,
    },
    {
      label: t('deliveryPeriodActual'),
      name: ProductFormFieldKeyE.LOCAL_SHIPPING_ACTUAL_DATETIME,
      type: 'datepicker-range',
      rules: [
        validateDateRange(
          ProductFormFieldKeyE.LOCAL_SHIPPING_ACTUAL_DATETIME_FROM,
          ProductFormFieldKeyE.LOCAL_SHIPPING_ACTUAL_DATETIME_TO,
        ),
      ],
      render: () => (
        <DateRange
          fromName={ProductFormFieldKeyE.LOCAL_SHIPPING_ACTUAL_DATETIME_FROM}
          toName={ProductFormFieldKeyE.LOCAL_SHIPPING_ACTUAL_DATETIME_TO}
          placeholder={[t('startDate'), t('endDate')]}
          format={DateHelper.FORMAT.FORMAT_2}
        />
      ),
      orderPosition: 12,
    },
    {
      label: t('storeArrivalResultDatetime'),
      name: ProductFormFieldKeyE.STORE_ARRIVAL_RESULT_DATETIME,
      type: 'datepicker-range',
      rules: [
        validateDateRange(
          ProductFormFieldKeyE.STORE_ARRIVAL_RESULT_DATETIME_FROM,
          ProductFormFieldKeyE.STORE_ARRIVAL_RESULT_DATETIME_TO,
        ),
      ],
      render: () => (
        <DateRange
          fromName={ProductFormFieldKeyE.STORE_ARRIVAL_RESULT_DATETIME_FROM}
          toName={ProductFormFieldKeyE.STORE_ARRIVAL_RESULT_DATETIME_TO}
          placeholder={[t('startDate'), t('endDate')]}
          format={DateHelper.FORMAT.FORMAT_2}
        />
      ),
      orderPosition: 14,
    },
    {
      label: t('remarksSearch'),
      name: ProductFormFieldKeyE.REMARKS,
      type: 'input',
      orderPosition: 15,
    },
    {
      label: t('productClassification'),
      name: ProductFormFieldKeyE.PRODUCT_TYPE,
      type: 'other',
      orderPosition: 16,
      render: () => {
        const handleRemoveTag = (codeToRemove: string) => {
          const updatedValues = productClassificationValues.filter(
            (item) => item.code !== codeToRemove,
          );
          setProductClassificationValues(updatedValues);

          // Also remove from the complete items
          const updatedItems = productClassificationItems.filter(
            (item) => item.code !== codeToRemove,
          );
          setProductClassificationItems(updatedItems);

          const selectedValuesString = updatedValues
            .map((item) => item.code)
            .join(', ');
          form.setFieldsValue({
            [ProductFormFieldKeyE.PRODUCT_TYPE]: selectedValuesString,
          });
        };

        return (
          <div className="flex w-full flex-wrap gap-[10px]">
            <div
              className="m-w-0 flex flex-[1_1_0%] flex-wrap gap-1"
              style={{
                minHeight: '30px',
                padding: '4px 8px',
                border: '1px solid var(--divider-color)',
                borderRadius: '4px',
                backgroundColor: 'var(--general-background)',
              }}
            >
              {productClassificationValues.map((item) => (
                <Tag
                  key={item.code}
                  label={item.label}
                  closable
                  onClose={() => handleRemoveTag(item.code)}
                  style={{
                    background: 'var(--sub-button-hover)',
                    border: '1px solid var(--link-text-table)',
                    borderRadius: '6px',
                    color: 'var(--link-text-table)',
                    display: 'flex',
                    maxWidth: '160px',
                  }}
                  closeIcon={
                    <CloseOutlined
                      style={{ color: 'var(--link-text-table)' }}
                    />
                  }
                />
              ))}
            </div>
            <Button
              className="flex-shrink-0"
              type="default"
              icon={<SearchOutlined />}
              label={t('search')}
              onClick={openProductClassificationModal}
              style={{ alignSelf: 'flex-start', borderRadius: '100px' }}
            />
          </div>
        );
      },
    },
  ];

  const ResponsiveForm = () => {
    const { isDeskTop } = useResponsive();

    const numberOfColumns = isDeskTop ? 2 : 1;

    const responsiveFields = formFields
      .filter((item) => item.type !== 'empty')
      .sort((a, b) => (a.orderPosition ?? 0) - (b.orderPosition ?? 0));

    const renderFields = isDeskTop ? formFields : responsiveFields;

    return (
      <Form
        form={form}
        formFields={renderFields}
        numOfColumns={numberOfColumns}
        onFinish={triggerSearch}
        onFinishFailed={(err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error('Validation failed:', err);
          }
        }}
      />
    );
  };

  return (
    <div>
      {ResponsiveForm()}

      <div className="mt-4 flex gap-x-2">
        <Button
          type="primary"
          icon={<SearchOutlined />}
          label={t('advancedSearch')}
          onClick={triggerSearch}
        />
        <Button
          icon={<ClearOutlined />}
          label={t('clear')}
          onClick={handleClear}
        />
      </div>
    </div>
  );
}
