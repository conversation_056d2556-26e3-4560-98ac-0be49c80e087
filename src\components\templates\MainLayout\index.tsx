'use client';

import Header from '@/components/organisms/Header';
import { useMounted } from '@/hooks';
import { toggleSidebar } from '@/redux/reducers/app-reducers';
import { RootStateT } from '@/redux/store';
import dynamic from 'next/dynamic';
import { ReactNode } from 'react';
import { useDispatch, useSelector } from 'react-redux';

const Sidebar = dynamic(() => import('@/components/organisms/Sidebar'), {
  ssr: false,
});

interface MainLayoutPropsI {
  children: ReactNode;
}

function MainLayout({ children }: MainLayoutPropsI) {
  const isMounted = useMounted();

  const isOpenSidebar = useSelector(
    (state: RootStateT) => state.app.isOpenSidebar,
  );

  const dispatch = useDispatch();

  const handleToggleSidebar = () => {
    dispatch(toggleSidebar());
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <aside
        className={`flex flex-col justify-between transition-all duration-300 ${
          isMounted && isOpenSidebar ? 'w-20' : 'w-[220px]'
        }`}
      >
        <Sidebar
          collapsed={isMounted && isOpenSidebar}
          setCollapsed={handleToggleSidebar}
        />
      </aside>

      <div className="flex min-w-0 flex-1 flex-col">
        <header className="flex items-center bg-white px-5 py-4 shadow">
          <Header />
        </header>
        <main className="flex flex-1 items-start justify-center overflow-auto bg-[var(--general-background)] p-5">
          <div className="w-full">{children}</div>
        </main>
      </div>
    </div>
  );
}

export default MainLayout;
