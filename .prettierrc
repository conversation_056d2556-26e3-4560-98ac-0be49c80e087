{"bracketSpacing": true, "semi": true, "printWidth": 80, "singleQuote": true, "trailingComma": "all", "tabWidth": 2, "useTabs": false, "endOfLine": "auto", "react/jsx-max-props-per-line": [1, {"when": "always"}], "plugins": ["prettier-plugin-organize-imports", "prettier-plugin-tailwindcss"], "importOrder": ["^react$", "^[./]", "^(../|../.*)"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "tailwindConfig": "./tailwind.config.ts"}