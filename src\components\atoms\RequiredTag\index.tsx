import { Text } from '@/components/atoms/Text';

interface RequiredTagPropsI {
  text?: string;
}

export const RequiredTag = ({ text = '必須' }: RequiredTagPropsI) => (
  <Text
    as="span"
    variant="caption"
    style={{
      backgroundColor: '#EC0B0B',
      color: 'white',
      position: 'absolute',
      left: '10px',
      padding: '5px 12px',
      fontSize: '11px',
      borderRadius: '5px',
    }}
  >
    {text}
  </Text>
);
