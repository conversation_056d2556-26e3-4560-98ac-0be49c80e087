'use client';

import {
  type PaginationProps as AntdPaginationProps,
  Table as AntdTable,
  type TableProps as AntdTableProps,
} from 'antd';
import classNames from 'classnames';
import { useTranslation } from 'next-i18next';
import type { CSSProperties } from 'react';
import { Pagination } from '../Pagination';
import { EmptyTable } from './EmptyTable';

export interface TablePropsI<T extends object>
  extends Omit<AntdTableProps<T>, 'pagination'> {
  onRefresh?: () => void;
  loadingMinHeight?: CSSProperties['minHeight'];
  pagination?:
    | (AntdPaginationProps & { extraActions?: React.ReactNode })
    | false;
}

export function Table<T extends object>({
  className,
  onRefresh,
  pagination,
  loadingMinHeight = '300px',
  ...props
}: TablePropsI<T>) {
  const { t } = useTranslation('components');

  return (
    <div className={classNames('w-full space-y-[20px]', className)}>
      {pagination !== false && (
        <Pagination onRefresh={onRefresh} {...pagination} />
      )}

      <AntdTable
        className="overflow-hidden rounded-md border border-[var(--divider-color)] [&_.ant-table-cell]:!border-[var(--divider-color)] [&_.ant-table-cell]:!px-4 [&_.ant-table-cell]:!py-[8.5px] [&_.ant-table-cell:last-child]:!border-r-0 [&_.ant-table-tbody_tr:last-child_.ant-table-cell]:!border-b-0 [&_.ant-table-thead]:whitespace-nowrap [&_.custom-row:hover_td]:!bg-yellow-50 [&_.first-column-bg]:!bg-[#f6f6f6] [&_th.ant-table-cell]:!bg-[#f6f6f6]"
        locale={{
          emptyText: (
            <EmptyTable
              message={t('table.noDataFound')}
              minHeight={loadingMinHeight}
            />
          ),
        }}
        pagination={false}
        bordered
        rowClassName={() => 'custom-row'}
        scroll={{ x: 600, ...props.scroll }}
        {...props}
      />

      {pagination !== false && (
        <Pagination onRefresh={onRefresh} {...pagination} />
      )}
    </div>
  );
}
