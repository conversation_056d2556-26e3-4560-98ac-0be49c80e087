import { Checkbox, Select } from '@/components/atoms';
import { QueryKeysE } from '@/enums/query-keys';
import { SelectE } from '@/enums/select';
import { MasterDataI } from '@/features/sample-information/types';
import { openModal } from '@/utils';
import { message } from 'antd';
import { ColumnType } from 'antd/es/table';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import ColorSizeModal from '../components/ColorSizeModal';
import { getMasterCodeMock, getStoreBudgetTypes } from '../services';
import { ProductDetailI, StoreBudgetTypeI } from '../types';

interface UseColorSizeListI {
  product?: ProductDetailI;
  dataTableMateria: Array<StoreBudgetTypeI>;
  setDataTableMateria: React.Dispatch<
    React.SetStateAction<Array<StoreBudgetTypeI>>
  >;
}

const useColorSizeList = ({
  product,
  dataTableMateria,
  setDataTableMateria,
}: UseColorSizeListI) => {
  const { t } = useTranslation('products');

  const { isLoading, data } = useQuery(
    [QueryKeysE.STORE_BUDGET_TYPES],
    getStoreBudgetTypes,
    {
      onSuccess: (data) => {
        if (!data?.data?.length) {
          message.warning(t('noDataFound'));

          return [];
        }

        return data.data;
      },
    },
  );

  useEffect(() => {
    if (data) {
      setDataTableMateria(data.data);
    }
  }, [data, setDataTableMateria]);

  const numberOfOrders = useMemo(
    () => dataTableMateria?.[0]?.orders?.length || 0,
    [dataTableMateria],
  );

  const transformMasterData = (data: { result: MasterDataI[] }) =>
    Array.isArray(data?.result)
      ? data.result.map((item: MasterDataI) => ({
          label: item[SelectE.M_CODE_FIELD_CODE_NAME],
          value: item[SelectE.M_CODE_FIELD_CODE],
        }))
      : [];

  const createQueryConfig = (codeKbn: string) => ({
    code_kbn: [codeKbn],
    hidden_flg: 0,
  });

  const querySize = useQuery(
    [QueryKeysE.SELECT_OPTIONS, 'size', 'list'],
    async () => {
      const data = await getMasterCodeMock(
        createQueryConfig(SelectE.M_CODE_SIZE),
      );

      return transformMasterData(data);
    },
  );

  const queryColor = useQuery(
    [QueryKeysE.SELECT_OPTIONS, 'color', 'list'],
    async () => {
      const data = await getMasterCodeMock(
        createQueryConfig(SelectE.M_CODE_COLOR),
      );

      return transformMasterData(data);
    },
  );

  const handleOpenDetailSizeColor = (record: StoreBudgetTypeI) => {
    const closeModal = openModal({
      title: t('sizeInformationDetails'),
      content: (
        <ColorSizeModal
          product={product}
          previousData={record}
          t={t}
          onClose={() => {
            closeModal();
          }}
        />
      ),
      footer: null,
    });
  };

  const createClickableSpan = (
    record: StoreBudgetTypeI,
    text?: string,
    isSize?: boolean,
  ) => {
    const { isRegistered } = record;

    return (
      <span
        style={{
          color:
            !isRegistered && !isSize
              ? 'var(--link-text-table)'
              : 'var(--main-text)',
          cursor: !isRegistered && !isSize ? 'pointer' : undefined,
          textDecoration: !isRegistered && !isSize ? 'underline' : undefined,
        }}
        onClick={
          !isRegistered && !isSize
            ? () => handleOpenDetailSizeColor(record)
            : undefined
        }
        className="max-w-full truncate"
      >
        {`［${text}］`}
      </span>
    );
  };

  const createSelectElement = (
    value: string,
    options: Array<{ label: string; value: string }>,
    onChange: (value: string) => void,
  ) => (
    <Select
      value={value || ''}
      options={options}
      className="w-full"
      onChange={onChange}
    />
  );

  const createFormatter =
    (config: {
      hasData: (record: StoreBudgetTypeI) => boolean;
      getText: (record: StoreBudgetTypeI) => string | undefined;
      getValue: (record: StoreBudgetTypeI) => string;
      options: Array<{ label: string; value: string }>;
      onChange: (record: StoreBudgetTypeI) => (value: string) => void;
      isSize: boolean;
    }) =>
    (_: any, record: StoreBudgetTypeI) => {
      if (config.hasData(record)) {
        return createClickableSpan(
          record,
          config.getText(record),
          config.isSize,
        );
      }

      return createSelectElement(
        config.getValue(record),
        config.options,
        config.onChange(record),
      );
    };

  const renderColorFormatter = createFormatter({
    hasData: (record) => !!record.pms_color,
    getText: (record) => record.pms_color?.code_name,
    getValue: (record) => record.pms_color_code,
    options: queryColor.data || [],
    onChange: (record: StoreBudgetTypeI) => (value: string) =>
      setDataTableMateria((pre) =>
        pre.map((item) =>
          item.id === record.id ? { ...item, pms_color_code: value } : item,
        ),
      ),
    isSize: false,
  });

  const renderSizeFormatter = createFormatter({
    hasData: (record) => !!(record.pms_size_code && record.pms_color),
    getText: (record) => record.pms_size_code,
    getValue: (record) => record.pms_size_code,
    options: querySize.data || [],
    onChange: (record: StoreBudgetTypeI) => (value: string) =>
      setDataTableMateria((pre) =>
        pre.map((item) =>
          item.id === record.id ? { ...item, pms_size_code: value } : item,
        ),
      ),
    isSize: true,
  });

  const createOrderFormatter = (
    record: StoreBudgetTypeI,
    orderIndex: number,
  ) => (
    <div className="flex items-center justify-center">
      <Checkbox
        type="checkbox"
        checked={record.orders?.[orderIndex]}
        value={record.orders?.[orderIndex]}
        onChange={(e) => {
          setDataTableMateria((pre) =>
            pre.map((item) =>
              item.id === record.id
                ? {
                    ...item,
                    orders: item.orders?.map((order, index) =>
                      index === orderIndex ? e.target.checked : order,
                    ),
                  }
                : item,
            ),
          );
        }}
      />
    </div>
  );

  const createOrderColumns = (): ColumnType<StoreBudgetTypeI>[] =>
    Array.from({ length: numberOfOrders }, (_, i) => ({
      title: t('order', { count: i + 1 }),
      key: 'orders',
      width: 150,
      render: (_: any, record: StoreBudgetTypeI) =>
        createOrderFormatter(record, i),
      align: 'center',
    }));

  const baseColumns: ColumnType<StoreBudgetTypeI>[] = [
    {
      title: t('no'),
      key: 'id',
      render: (_: any, _record: StoreBudgetTypeI, index: number) => index + 1,
      width: 50,
      align: 'center',
      className: 'bg-[#F6F6F6]',
    },
    {
      title: <div className="text-center">{t('color')}</div>,
      key: 'pms_color_code',
      width: 200,
      render: renderColorFormatter,
    },
    {
      title: <div className="text-center">{t('size')}</div>,
      key: 'pms_size_code',
      width: 200,
      render: renderSizeFormatter,
    },
  ];

  const columns: ColumnType<StoreBudgetTypeI>[] = [
    ...baseColumns,
    ...createOrderColumns(),
  ];

  const createNewRowData = (currentLength: number) => ({
    id: currentLength + 1,
    pms_color_code: '',
    pms_size_code: '',
    orders: Array.from({ length: numberOfOrders }, () => false),
    isRegistered: false,
  });

  const handleAddRow = () => {
    setDataTableMateria((prevData) => [
      ...prevData,
      createNewRowData(prevData.length),
    ]);
  };

  return {
    isLoading,
    dataTableMateria,
    columns,
    handleAddRow,
    setDataTableMateria,
  };
};

export default useColorSizeList;
