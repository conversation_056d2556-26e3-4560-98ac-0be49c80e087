import { Button, Select, Text } from '@/components/atoms';
import { Table } from '@/components/atoms/Table';
import usePagination from '@/hooks/usePagination';
import { MasterCodeDataI } from '@/types/common';
import { Radio as AntdRadio } from 'antd';
import { useTranslation } from 'next-i18next';
import React, { useEffect, useMemo, useState } from 'react';
import { useStoreBudgetTypeModal } from '../../hooks/useStoreBudgetTypeModal';
import { ProductStoreBudgetI } from '../../types';

interface ProductCategoryModalPropsI {
  onSelect?: (selected: ProductStoreBudgetI) => void;
}

const StoreBudgetTypeModal: React.FC<ProductCategoryModalPropsI> = ({
  onSelect,
}) => {
  const { t } = useTranslation('products');
  const { pagination, setDefaultPage } = usePagination();
  const [selectedId, setSelectedCode] = useState<number | null>(null);
  const [genderFilter, setGenderFilter] = useState<string>('');

  const { storeBudgets, genderCategories, isFetching, columns, refetch } =
    useStoreBudgetTypeModal(
      pagination.current,
      pagination.pageSize,
      setDefaultPage,
    );

  useEffect(() => {
    setDefaultPage();
  }, [genderFilter]);

  const filteredData = useMemo(() => {
    const filtered = genderFilter
      ? storeBudgets.filter(
          (item: ProductStoreBudgetI) =>
            item.gender_category_code === genderFilter,
        )
      : storeBudgets;

    const groupedMap = new Map<string, ProductStoreBudgetI[]>();
    filtered.forEach((item: ProductStoreBudgetI) => {
      const key = item.gender_category_code || 'none';
      if (!groupedMap.has(key)) groupedMap.set(key, []);
      groupedMap.get(key)!.push(item);
    });

    const flattened: ProductStoreBudgetI[] = [];
    groupedMap.forEach((group) => {
      group.forEach((item, index) => {
        flattened.push({
          ...item,
          gender_category_name: index === 0 ? item.gender_category_name : '',
        });
      });
    });

    return flattened;
  }, [storeBudgets, genderFilter]);

  const handleConfirmSelect = () => {
    const selectedItem = filteredData?.find(
      (item: ProductStoreBudgetI) => item.id == selectedId,
    );
    if (onSelect) {
      onSelect({
        id: selectedItem?.id,
        gender_category_code: selectedItem?.gender_category_code,
        store_budget_type_code: selectedItem?.store_budget_type_code,
        store_budget_type_name: selectedItem?.store_budget_type_name,
      });
    }
  };

  return (
    <div>
      <div className="mt-6 mb-8 flex" style={{ width: 400 }}>
        <Text variant="span" className="mt-2 mr-2 w-30">
          {t('gender')}
        </Text>
        <Select
          allowClear
          value={genderFilter}
          onChange={(value) => {
            setGenderFilter(value || '');
            setDefaultPage();
          }}
          options={[
            { label: '', value: '' },
            ...(genderCategories?.map((item: MasterCodeDataI) => ({
              label: item.code_name,
              value: item.code,
            })) ?? []),
          ]}
          style={{ width: '100%' }}
        />
      </div>

      <AntdRadio.Group
        className="w-full"
        value={selectedId}
        onChange={(e) => setSelectedCode(e.target.value)}
      >
        <Table<ProductStoreBudgetI>
          columns={columns}
          dataSource={filteredData}
          rowKey="id"
          pagination={{
            ...pagination,
            total: filteredData?.length || 0,
          }}
          size="small"
          scroll={{ y: 300 }}
          loading={isFetching}
          onRefresh={refetch}
          loadingMinHeight="20px"
        />
      </AntdRadio.Group>

      <div style={{ marginTop: 24, textAlign: 'left' }}>
        <Button
          type="primary"
          label={t('choise')}
          onClick={handleConfirmSelect}
        />
      </div>
    </div>
  );
};

export default StoreBudgetTypeModal;
