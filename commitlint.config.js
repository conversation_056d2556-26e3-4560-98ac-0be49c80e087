/* eslint-disable @typescript-eslint/naming-convention */
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat', // New feature
        'fix', // Bug fix
        'docs', // Documentation changes
        'style', // Formatting, no code change
        'refactor', // Code refactoring
        'perf', // Performance improvements
        'test', // Adding or updating tests
        'build', // Build system or external dependencies
        'ci', // CI configuration
        'chore', // Other changes that don't modify src or test
        'revert', // Revert a previous commit
        'base', // Base source code changes (initial setup, core structure)
        'format', // Code formatting changes (applying Prettier or ESLint fixes)
      ],
    ],
  },
};
