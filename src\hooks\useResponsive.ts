import { useEffect, useState } from 'react';

export function useResponsive() {
  const [deviceStates, setDeviceStates] = useState({
    isMobile: false,
    isTablet: false,
    isDeskTop: false,
  });

  useEffect(() => {
    function updateBreakpoint() {
      const width = window.innerWidth;
      const breakpoints = { mobile: 0, tablet: 768, desktop: 1024 };

      setDeviceStates({
        isMobile: width >= breakpoints.mobile,
        isTablet: width >= breakpoints.tablet,
        isDeskTop: width >= breakpoints.desktop,
      });
    }

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);

    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return deviceStates;
}
