import SearchWithModal from '@/components/atoms/SearchWithModal';
import { useTranslation } from 'next-i18next';
import StoreBudgetTypeModal from '.';
import { ProductStoreBudgetI } from '../../types';

export const StoreBudgetField = ({
  selected,
  onSelect,
}: {
  selected: ProductStoreBudgetI | null;
  onSelect: (value: ProductStoreBudgetI, onClose?: () => void) => void;
}) => {
  const { t } = useTranslation('products');

  return (
    <SearchWithModal
      title={t('storeBudgetCategorySelection')}
      label={selected?.store_budget_type_name ?? ''}
      textSearch={t('search')}
      content={({ onClose }) => (
        <StoreBudgetTypeModal onSelect={(value) => onSelect(value, onClose)} />
      )}
    />
  );
};
