import { FormInstance } from 'antd';
import { useTranslation } from 'next-i18next';
import { useCallback, useState } from 'react';
import { getProductOrderFormFields } from './useProductOrderFormFields';
import { getProductOrderMessages } from './useProductOrderMessages';
import {
  getContractColumns,
  getPurchasePriceColumns,
  getSupplierFactoryColumns,
} from './useProductOrderTableColumns';
import { handleValidatePrice } from './useProductOrderValidation';
import { useRetailPriceModal } from './useRetailPriceModal';
import { useSupplierFactory } from './useSupplierFactory';

export const useProductOrderCreate = () => {
  const { t } = useTranslation(['product-order', 'common']);

  const [isSupplierFactoryModalOpen, setIsSupplierFactoryModalOpen] =
    useState(false);
  const [isContractModalOpen, setIsContractModalOpen] = useState(false);
  const [contractIndex, setContractIndex] = useState(0);
  const [contractModalType, setContractModalType] = useState<'create' | 'edit'>(
    'create',
  );

  const handleOpenSupplierFactoryModal = () =>
    setIsSupplierFactoryModalOpen(true);
  const handleCloseSupplierFactoryModal = () =>
    setIsSupplierFactoryModalOpen(false);

  const handleOpenContractModal = (
    type: 'create' | 'edit' = 'edit',
    index: number = 0,
  ) => {
    setContractIndex(index);
    setContractModalType(type);
    setIsContractModalOpen(true);
  };

  const handleCloseContractModal = () => {
    setIsContractModalOpen(false);
  };

  const handleSubmit = async (basicForm: FormInstance) => {
    const basicValues = await basicForm.validateFields();

    return { basicValues };
  };

  const handleDelete = useCallback(async () => {
    // Implement delete logic here
  }, []);

  const {
    supplierFactories,
    setSupplierFactories,
    handleSubmitSupplierFactoryModal,
    handleCheckboxChange,
  } = useSupplierFactory();

  const {
    isOpen: isRetailPriceModalOpen,
    setIsOpen: setIsRetailPriceModalOpen,
    handleCloseModal: handleCloseRetailPriceModal,
  } = useRetailPriceModal();

  const purchasePriceColumns = getPurchasePriceColumns(t, handleValidatePrice);
  const contractColumns = getContractColumns(t, handleOpenContractModal);
  const supplierFactoryColumns = getSupplierFactoryColumns(
    t,
    handleCheckboxChange,
  );

  const basicFormFields = getProductOrderFormFields(t, {
    setIsRetailPriceModalOpen,
    handleOpenSupplierFactoryModal,
    supplierFactories,
    setSupplierFactories,
    supplierFactoryColumns,
  });

  const {
    orderConfirmationMessage,
    salesDepartmentMessage,
    processorMessage,
    inspectorMessage,
  } = getProductOrderMessages(t);

  return {
    // 基本フォームフィールド (Basic tab)
    basicFormFields,
    handleSubmit,
    handleDelete,
    // 小売価格モーダル関連の状態とハンドラー (Supplier factory modal)
    setSupplierFactories,
    isSupplierFactoryModalOpen,
    handleSubmitSupplierFactoryModal,
    handleCloseSupplierFactoryModal,
    // 伝達メッセージ関連の状態 (Convey message)
    orderConfirmationMessage,
    salesDepartmentMessage,
    processorMessage,
    inspectorMessage,
    // 購入価格関連の状態とハンドラー (Purchase price)
    purchasePriceColumns,
    // 契約関連の状態とハンドラー (Contract modal)
    contractColumns,
    isContractModalOpen,
    handleOpenContractModal,
    handleCloseContractModal,
    contractIndex,
    contractModalType,
    // 小売価格モーダル関連の状態とハンドラー (Retail price modal)
    isRetailPriceModalOpen,
    setIsRetailPriceModalOpen,
    handleCloseRetailPriceModal,
  };
};
