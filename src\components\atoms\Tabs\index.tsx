import { Tabs as AntdTabs, TabsProps as AntdTabsProps } from 'antd';
import { styled } from 'styled-components';

export const Tabs = (props: AntdTabsProps) => {
  return <AntdTabs {...props} />;
};

export const StyledTabs = styled(AntdTabs)`
  .ant-tabs-nav {
    margin: 0;
  }

  .ant-tabs-nav-wrap {
    /* border-bottom: var(--boder) !important; */
  }

  .ant-tabs-tab {
    background: #f0f0f0;
    border-radius: 8px 8px 0 0 !important;
    border: 1px solid #d9d9d9;
    margin: unset !important;
    margin-right: 4px !important;
    margin-left: 10px;
    padding: 0 40px;
    height: 34px;
  }

  .ant-tabs-tab:not(.ant-tabs-tab-active) {
    border-radius: 8px 8px 8px 8px !important;
    background-color: #aaaaaa !important;
    color: #ffffff !important;
  }

  .ant-tabs-tab-active {
    background: white;
    border-top: 3px solid var(--button-accent-text);
    border-bottom: 0px solid #d9d9d9 !important;
    height: 38px !important;
    border-bottom: 2px solid transparent !important;
    z-index: 2;
  }

  .ant-tabs-tab-active .ant-tabs-tab-btn {
    color: var(--button-accent-text) !important;
  }
`;
