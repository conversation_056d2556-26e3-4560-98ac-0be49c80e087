import { ProductOrderFormFieldKeyE } from './enums';

export interface ProductOrderDetailI {
  id: number;
  [ProductOrderFormFieldKeyE.PMS_ORDER_SBT_CODE]: string;
  [ProductOrderFormFieldKeyE.PMS_ORDER_DATETIME]: string;
  [ProductOrderFormFieldKeyE.PMS_JODAI_TAX_OUT]: string;
  [ProductOrderFormFieldKeyE.PMS_JODAI_TAX_IN]: string;
  [ProductOrderFormFieldKeyE.PMS_ORDER_CALC_RATE]: string;
  [ProductOrderFormFieldKeyE.COUNTRY_ORIGIN_CODE]: string;
  [ProductOrderFormFieldKeyE.IMPORT_METHOD_CODE]: string;
  [ProductOrderFormFieldKeyE.IMPORT_COST_UNIT_CODE]: string;
  [ProductOrderFormFieldKeyE.PAYMENT_KGN]: string;
  [ProductOrderFormFieldKeyE.PMS_MANAGER_CODE]: string;
  [ProductOrderFormFieldKeyE.SUPPLIER_FILTER]: string;
  [ProductOrderFormFieldKeyE.PMS_SUPPLIER_CODE]: string;
  [ProductOrderFormFieldKeyE.PMS_SUPPLIER_FACTORY_CODE]: string;
  [ProductOrderFormFieldKeyE.INSPECT_PRE_FLG]: boolean;
  [ProductOrderFormFieldKeyE.INSPECT_PRE_STAFF_CODE]: string;
  [ProductOrderFormFieldKeyE.INSPECT_PRE_DATETIME]: string;
  [ProductOrderFormFieldKeyE.INSPECT_PRE_REMARKS]: string;
  [ProductOrderFormFieldKeyE.PART_NUMBER]: string;
  [ProductOrderFormFieldKeyE.PRODUCT_NAME]: string;
  [ProductOrderFormFieldKeyE.SELLING_POINT]: string;
  [ProductOrderFormFieldKeyE.OTHER_NOTES]: string;
  [ProductOrderFormFieldKeyE.COMMENT]: string;
  [ProductOrderFormFieldKeyE.PROCESSING_DATE]: string;
  [ProductOrderFormFieldKeyE.COMPANY_NAME]: string;
  [ProductOrderFormFieldKeyE.CONTRACT_DATE]: string;
  [ProductOrderFormFieldKeyE.NOTE]: string;
  created_at: string;
  updated_at: string;
  created_by: {
    name: string;
  };
  updated_by: {
    name: string;
  };
  purchase_prices: {
    no: number;
    color: string;
    size: string;
    price_tax_ex: number;
    price_tax_in: number;
    fob: number;
  }[];
  product_confirmations: {
    [ProductOrderFormFieldKeyE.PMS_PRODUCT_NO]: string;
    [ProductOrderFormFieldKeyE.PMS_PRODUCT_NAME]: string;
  };
  convey_msg_1: {
    [ProductOrderFormFieldKeyE.CONVEY_MSG]: string;
  };
  convey_msg_2: {
    [ProductOrderFormFieldKeyE.CONVEY_MSG]: string;
  };
  convey_msg_3: {
    [ProductOrderFormFieldKeyE.CONVEY_MSG]: string;
  };
  convey_msg_4: {
    [ProductOrderFormFieldKeyE.CONVEY_MSG]: string;
  };
  convey_msg_5: {
    [ProductOrderFormFieldKeyE.CONVEY_MSG]: string;
  };
  contracts: ContractI[];
  supplier_factories?: SupplierFactoryI[];
}

export interface ConveyMessageI {
  title: string;
  buttonLabel: string;
  descriptions: {
    name: ProductOrderFormFieldKeyE;
    height?: number;
    label: string;
    icon?: React.ReactNode;
  }[];
}

export interface SupplierFactoryI {
  id: number;
  delete: boolean;
  pms_supplier_factory_code: string;
  pms_supplier_name: string;
}

export interface ContractI {
  no: number;
  company_name: string;
  contract_date: string;
  note: string;
  created_at: string;
  updated_at: string;
  created_by: {
    name: string;
  };
  updated_by: {
    name: string;
  };
}

export interface RetailPriceI {
  id: number;
  code: string;
  code_name: string;
}
