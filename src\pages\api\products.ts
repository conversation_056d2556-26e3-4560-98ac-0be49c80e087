import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from '@/constants/common';
import { ProductI } from '@/features/product-information/types';
import { faker } from '@faker-js/faker';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method === 'GET') {
    const { page = DEFAULT_PAGE, per_page = DEFAULT_PAGE_SIZE } = req.query;

    await new Promise((resolve) => setTimeout(resolve, 400));

    // Generate all products first
    const products: ProductI[] = Array.from(
      { length: 50 },
      (_, i) =>
        ({
          id: i + 1,
          product_no:
            faker.string.numeric(2) +
            '-' +
            faker.string.numeric(9) +
            '-' +
            faker.string.numeric(2),
          product_name: faker.string.alphanumeric(20),
          sample_no:
            faker.string.alphanumeric(3) + '-' + faker.string.numeric(3),
          gender_category_code: faker.helpers.arrayElement([
            'M',
            'W',
            'KIDS(BOYS)',
            'KIDS(GIRLS)',
          ]),
          product_category_code: faker.commerce.productMaterial(),
          planner_code: faker.person.fullName(),
          product_remarks: faker.lorem.sentence(),
          product_media: {
            id: i + 1,
            product_fkid: faker.string.alphanumeric(2),
            product_media_file_kbn: faker.helpers.arrayElement([
              'PMS_PRODUCT_IMAGE',
              'PMS_PRODUCT_PATTERN',
              'PMS_SPEC_SAMPLE_IMAGE',
              'PMS_SPEC_DESGIN_IMAGE',
            ]),
            media_file_fkid:
              faker.string.alphanumeric(3) + '-' + faker.string.numeric(3),
            media_file: {
              media_file_kbn: faker.string.alphanumeric(2),
              media_file_logical_name: faker.system.fileName(),
              media_file_physical_name: faker.system.fileName(),
              media_file_seq_no: faker.number.int({ min: 1, max: 10 }),
              sort_key: faker.number.int({ min: 1, max: 100 }),
              media_file_path: faker.image.urlLoremFlickr(),
            },
          },
        }) as ProductI,
    );

    // Apply pagination
    const start = (Number(page) - 1) * Number(per_page);
    const end = start + Number(per_page);
    const paginatedProducts = products.slice(start, end);
    const sumOfPage = Math.ceil(products.length / Number(per_page));

    res.status(200).json({
      message: 'データの取得に成功しました。',
      result: {
        data: paginatedProducts,
        meta: {
          current_page: Number(page),
          from: start + 1,
          last_page: sumOfPage,
          per_page: Number(per_page),
          to: end,
          total: products.length,
        },
      },
    });
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
