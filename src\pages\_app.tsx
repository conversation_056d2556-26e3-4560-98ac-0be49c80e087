import { store } from '@/redux/store';
import { antdTheme } from '@/styles/antd-theme';
import '@/styles/globals.css';
import { ConfigProvider } from 'antd';
import enUS from 'antd/locale/en_US';
import jaJP from 'antd/locale/ja_JP';
import { appWithTranslation } from 'next-i18next';
import type { AppProps } from 'next/app';
import { useRouter } from 'next/router';
import { QueryClientProvider } from 'react-query';
import { Provider } from 'react-redux';
import { queryClient } from '../configs/query-client';

function App({ Component, pageProps }: AppProps) {
  const { locale } = useRouter();
  const antdLocale = locale === 'ja' ? jaJP : enUS;

  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider theme={antdTheme} locale={antdLocale}>
          <Component {...pageProps} />
        </ConfigProvider>
      </QueryClientProvider>
    </Provider>
  );
}

export default appWithTranslation(App);
