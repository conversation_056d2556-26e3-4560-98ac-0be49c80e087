import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from '@/constants/common';
import { OrderInformationI } from '@/features/products/types';
import { faker } from '@faker-js/faker';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method === 'GET') {
    const {
      page = DEFAULT_PAGE,
      per_page = DEFAULT_PAGE_SIZE,
      id: productId,
    } = req.query;

    await new Promise((resolve) => setTimeout(resolve, 400));

    const length = productId === '50' ? 0 : 50;

    const data: OrderInformationI[] = Array.from({ length }, (_, i) => ({
      id: i + 1,
      t_pms_product_fkid: i + 1,
      pms_order_cnt: i + 1,
      pms_order_sbt_code: 'xxx',
      pms_order_datetime: '2025-07-02 15:15:28',
      pms_jodai_tax_out: '3.000',
      pms_jodai_tax_in: '12.000',
      pms_order_calc_rate: 33,
      country_origin_code: 'xxx',
      import_method_code: 'xxx',
      import_cost_unit_code: 'xxx',
      payment_kgn: '3300.000',
      pms_manager_code: 'xxds',
      pms_supplier_code: 'xxx',
      inspect_pre_flg: 0,
      inspect_pre_datetime: null,
      inspect_pre_staff_code: 'xxx',
      inspect_pre_note: null,
      media_file_path: faker.image.urlLoremFlickr(),
      pms_shippings: Array.from(
        {
          length: faker.number.int({ min: 0, max: 3 }),
        },
        (_, idx) => ({
          id: `${i + 1}-${idx + 1}`,
          t_pms_product_fkid: idx + 1,
          pms_order_cnt: idx + 1,
          pms_shipping_cnt: idx + 1,
          local_shipping_plan_datetime: '2025-07-22 15:17:33',
          local_shipping_plan_week_no_n: idx * 2,
          local_shipping_result_datetime: null,
          local_shipping_result_week_no_n: 4,
          store_arrival_plan_datetime: '2025-07-09 15:17:33',
          store_arrival_plan_week_no_n: null,
          store_arrival_result_datetime: null,
          store_arrival_result_week_no_n: null,
          country_origin_code: 'bdf',
          payment_plan_ym: 'dfg',
          fec_no: 'dfg',
          fec_rate: idx * 146.24,
          fec_reserve_datetime: null,
          fec_use_start_datetime: null,
          fec_use_end_datetime: null,
          number_check_flg: 0,
          shipping_problem_flg: 0,
          pms_mgmt_tbl_exclude_flg: 0,
          pms_stats_exclude_flg: 0,
          pms_supplier_stats_exclude_flg: 0,
          delivery_complete_flg: 0,
        }),
      ),
    }));
    const start = (Number(page) - 1) * Number(per_page);
    const end = start + Number(per_page);
    const paginatedDatas = data.slice(start, end);
    const sumOfPage = Math.ceil(data.length / Number(per_page));

    res.status(200).json({
      status: 200,
      result: {
        data: paginatedDatas,
        links: {
          first: 'http://localhost:8888/api/v1/store-budget-types?page=1',
          last: 'http://localhost:8888/api/v1/store-budget-types?page=1',
          prev: null,
          next: null,
        },
        meta: {
          current_page: Number(page),
          from: start + 1,
          last_page: sumOfPage,
          per_page: Number(per_page),
          to: end,
          total: data.length,
        },
      },
      message: 'データの取得に成功しました。',
    });
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
