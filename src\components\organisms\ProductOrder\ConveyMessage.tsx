import { Button } from '@/components/atoms';
import { Descriptions } from 'antd';
import { Fragment } from 'react';

interface ConveyMessagePropsI {
  tableContent: {
    title: string;
    buttonLabel: string;
    descriptions: {
      label: string;
      name: string;
      height?: number;
      icon?: React.ReactNode;
    }[];
  };
  values: Record<string, string>[];
  handleClickButton: () => void;
}

const ConveyMessage = ({
  tableContent,
  values,
  handleClickButton,
}: ConveyMessagePropsI) => {
  return (
    <Fragment>
      <div className="mb-4" key={tableContent.title}>
        <div className="flex items-start justify-between rounded-t bg-white px-2 py-1">
          <div className="flex items-center">
            <span className="mr-2 pt-2 text-xs font-bold">
              {tableContent.title}
            </span>
          </div>
          <Button
            label={tableContent.buttonLabel}
            size="small"
            type="default"
            className="h-6 rounded border border-green-400 bg-white px-2 py-0.5 text-xs font-normal text-green-700 hover:border-green-500 hover:text-green-800"
            style={{ lineHeight: '1', minHeight: 'unset' }}
            onClick={handleClickButton}
          />
        </div>
        <Descriptions
          size="small"
          column={1}
          bordered
          className="text-xs"
          labelStyle={{
            padding: '4px 8px',
            textAlign: 'right',
            border: '1px solid #BBBBBB',
            color: '#222222',
            width: 150,
          }}
          contentStyle={{
            padding: '4px 8px',
            border: '1px solid #BBBBBB',
            color: '#222222',
            background: '#EDEDED',
            borderRadius: '4px',
          }}
        >
          {tableContent?.descriptions?.map((description, index) => (
            <Fragment key={description.label}>
              <Descriptions.Item
                label={description.label}
                style={{ height: description.height }}
              >
                {Array.isArray(values)
                  ? values[index]?.[description.name]
                  : values?.[description.name]}
                <span className="ml-2">{description.icon}</span>
              </Descriptions.Item>
            </Fragment>
          ))}
        </Descriptions>
      </div>
    </Fragment>
  );
};

export default ConveyMessage;
