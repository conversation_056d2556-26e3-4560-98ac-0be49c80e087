import { Button, StyledTabs } from '@/components/atoms';
import InformationUserCreateUpdate from '@/components/organisms/Footer/InformationUserCreateUpdate';
import ConveyMessage from '@/components/organisms/ProductOrder/ConveyMessage';
import ContractModal from '@/features/product-order/components/contractModal';
import BasicTab from '@/features/product-order/components/ProductOrderTabs/BasicTab';
import ContractTab from '@/features/product-order/components/ProductOrderTabs/ContractTab';
import MaterialAccessoriesTab from '@/features/product-order/components/ProductOrderTabs/MaterialAccessoriesTab';
import OrderHistoryTab from '@/features/product-order/components/ProductOrderTabs/OrderHistoryTab';
import ProductSpecTab from '@/features/product-order/components/ProductOrderTabs/ProductSpecTab';
import PurchasePriceTab from '@/features/product-order/components/ProductOrderTabs/PurchasePriceTab';
import ShippingInfoTab from '@/features/product-order/components/ProductOrderTabs/ShippingInfoTab';
import RetailPriceModal from '@/features/product-order/components/retailPriceModal';
import SupplierFactoryModal from '@/features/product-order/components/supplierFactoryModal';
import { useProductOrderDetail } from '@/features/product-order/hooks/useProductOrderDetail';
import { ProductOrderDetailI } from '@/features/product-order/types';
import { withMainLayout } from '@/hocs/withMainLayout';
import { DateHelper } from '@/utils/date';
import {
  getStaticPaths,
  getStaticTranslationsProps,
} from '@/utils/localization';
import { Form as AntdForm, Collapse, Grid } from 'antd';
import { useTranslation } from 'next-i18next';
import { Fragment, useCallback, useEffect, useState } from 'react';

interface ProductOrderDetailPagePropsI {
  type: 'create' | 'edit';
}

const ProductOrderDetailPage = ({
  type = 'edit',
}: ProductOrderDetailPagePropsI) => {
  const { t } = useTranslation(['product-order', 'common']);
  const [basicForm] = AntdForm.useForm();
  const [purchasePrice, setPurchasePrice] = useState<
    ProductOrderDetailI['purchase_prices']
  >([]);
  const [contract, setContract] = useState<ProductOrderDetailI['contracts']>(
    [],
  );

  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();
  const numberOfColumns = screens.lg ? 2 : 1;

  const {
    data,
    basicFormFields,
    handleSubmit,
    handleDelete,
    isSupplierFactoryModalOpen,
    handleSubmitSupplierFactoryModal,
    handleCloseSupplierFactoryModal,
    orderConfirmationMessage,
    salesDepartmentMessage,
    processorMessage,
    inspectorMessage,
    purchasePriceColumns,
    contractColumns,
    isContractModalOpen,
    handleCloseContractModal,
    handleOpenContractModal,
    contractIndex,
    contractModalType,
    setSupplierFactories,
    isRetailPriceModalOpen,
    setIsRetailPriceModalOpen,
  } = useProductOrderDetail();

  const productOrder = data?.result;

  const getBasicFormInitialValues = useCallback(() => {
    return {
      ...productOrder,
      pms_order_datetime: productOrder?.pms_order_datetime
        ? DateHelper.toDayjs(productOrder.pms_order_datetime)
        : null,
      inspect_pre_datetime: productOrder?.inspect_pre_datetime
        ? DateHelper.toDayjs(productOrder.inspect_pre_datetime)
        : null,
    };
  }, [productOrder]);

  useEffect(() => {
    basicForm.setFieldsValue(getBasicFormInitialValues());

    if (productOrder?.contracts) {
      setContract(productOrder?.contracts);
    }

    if (productOrder?.supplier_factories) {
      setSupplierFactories(productOrder?.supplier_factories);
    }
  }, [
    productOrder,
    basicForm,
    getBasicFormInitialValues,
    setSupplierFactories,
  ]);

  const copyFirstPurchasePrice = () => {
    if (
      purchasePrice &&
      Array.isArray(purchasePrice) &&
      purchasePrice.length > 0
    ) {
      const firstItem = purchasePrice[0];

      if (firstItem && firstItem.price_tax_ex) {
        const updatedPurchasePrice = purchasePrice.map((item) => {
          return {
            ...item,
            price_tax_ex: firstItem.price_tax_ex,
            price_tax_in: firstItem.price_tax_ex,
            fob: firstItem.price_tax_ex,
          };
        });

        setPurchasePrice(updatedPurchasePrice);
      }
    }
  };

  const copyPurchasePriceFromDropdown = () => {
    setPurchasePrice(productOrder?.purchase_prices);
  };

  const orderInfoTabItems = [
    {
      key: 'basic',
      label: t('common:basic'),
      children: (
        <BasicTab
          form={basicForm}
          numOfColumns={numberOfColumns}
          formFields={
            numberOfColumns === 2
              ? basicFormFields.sort(
                  (a, b) => (a.orderPosition || 0) - (b.orderPosition || 0),
                )
              : basicFormFields
          }
        />
      ),
    },
    {
      key: 'purchase-price',
      label: t('purchasePrice'),
      children: (
        <PurchasePriceTab
          purchasePrice={purchasePrice}
          setPurchasePrice={setPurchasePrice}
          purchasePriceColumns={purchasePriceColumns}
          copyFirstPurchasePrice={copyFirstPurchasePrice}
          copyPurchasePriceFromDropdown={copyPurchasePriceFromDropdown}
        />
      ),
    },
    {
      key: 'contract',
      label: t('contract'),
      children: (
        <ContractTab
          contract={contract}
          setContract={setContract}
          contractColumns={contractColumns}
          handleOpenContractModal={handleOpenContractModal}
        />
      ),
    },
  ];

  const productOrderTabItems = [
    {
      key: 'order-info',
      label: t('orderInfo'),
      children: (
        <Fragment>
          <div className="mt-4 mb-4">
            <Button
              label={t('addShippingInfo')}
              type="primary"
              size="small"
              className="mb-2"
            />
          </div>
          <StyledTabs items={orderInfoTabItems} />
        </Fragment>
      ),
    },
    {
      key: 'shipping-info',
      disabled: type === 'create',
      label: t('shippingInfo'),
      children: <ShippingInfoTab label={t('shippingInfo')} />,
    },
    {
      key: 'product-spec',
      disabled: type === 'create',
      label: t('productSpec'),
      children: <ProductSpecTab label={t('productSpec')} />,
    },
    {
      key: 'material-accessories',
      disabled: type === 'create',
      label: t('materialAccessories'),
      children: <MaterialAccessoriesTab label={t('materialAccessories')} />,
    },
    {
      key: 'order-history',
      disabled: type === 'create',
      label: t('orderHistory'),
      children: <OrderHistoryTab label={t('orderHistory')} />,
    },
  ];

  const handleClickButton = () => {
    // TODO: モーダルを開く
  };

  const handleSelectRetailPrice = () => {
    // TODO: モーダルを閉じる
  };

  return (
    <Fragment>
      <ConveyMessage
        tableContent={orderConfirmationMessage}
        values={productOrder?.product_confirmations}
        handleClickButton={handleClickButton}
      />

      <Collapse
        items={[
          {
            key: 'message',
            label: <span className="font-bold">{t('message')}</span>,
            children: (
              <>
                <ConveyMessage
                  values={[
                    productOrder?.convey_msg_1,
                    productOrder?.convey_msg_2,
                  ]}
                  tableContent={salesDepartmentMessage}
                  handleClickButton={handleClickButton}
                />
                <ConveyMessage
                  values={[
                    productOrder?.convey_msg_3,
                    productOrder?.convey_msg_4,
                  ]}
                  tableContent={processorMessage}
                  handleClickButton={handleClickButton}
                />
                <ConveyMessage
                  values={[productOrder?.convey_msg_5]}
                  tableContent={inspectorMessage}
                  handleClickButton={handleClickButton}
                />
              </>
            ),
          },
        ]}
        style={{ marginBottom: 30 }}
      />
      <StyledTabs items={productOrderTabItems} />
      <InformationUserCreateUpdate
        type={type}
        handleSubmit={() => handleSubmit(basicForm)}
        handleDelete={() => handleDelete}
        data={productOrder}
      />
      <SupplierFactoryModal
        isOpen={isSupplierFactoryModalOpen}
        onSubmit={handleSubmitSupplierFactoryModal}
        onClose={handleCloseSupplierFactoryModal}
      />
      <ContractModal
        isOpen={isContractModalOpen}
        onSubmit={handleCloseContractModal}
        onClose={handleCloseContractModal}
        initialValues={contract[contractIndex]}
        type={contractModalType}
      />
      <RetailPriceModal
        isOpen={isRetailPriceModalOpen}
        onClose={() => setIsRetailPriceModalOpen(false)}
        modalTitle={t('retailPriceExclTaxYen')}
        onSelect={handleSelectRetailPrice}
      />
    </Fragment>
  );
};

export { getStaticPaths };

export const getStaticProps = getStaticTranslationsProps([
  'product-order',
  'common',
]);

export default withMainLayout(ProductOrderDetailPage);
