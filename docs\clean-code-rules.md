## ✅ **1. Naming Conventions**

- Use **PascalCase** for components and pages: `UserProfile.tsx`
- Use **camelCase** for functions, variables, and hooks: `useFetchData`
- Use **UPPER_CASE** for constants: `DEFAULT_PAGE_SIZE`

---

## ✅ **2. File & Component Design**

- One component per file
- Component name should match file name
- Keep components **small** and **focused**
- Prefer **functional components** and **React Hooks**

---

## ✅ **3. Avoid Magic Strings/Numbers**

Bad:

```tsx
if (user.role === 'admin') { ... }
```

Good:

```tsx
const ROLE_ADMIN = 'admin';
if (user.role === ROLE_ADMIN) { ... }
```

---

## ✅ **4. TypeScript Usage**

- Always type your props and states
- Use `interface` or `type` for data structures

```tsx
interface UserI {
  id: number;
  name: string;
}
```

---

## ✅ **5. API Handling (Server + Client)**

- Use **`fetch`/`axios` wrappers** in `lib` or `services`
- Separate API logic from UI
- Use **React Query** for data fetching (especially client-side)

---

## ✅ **6. Pages and Routing**

- Keep `pages/` folder clean
- Use nested routes for layout grouping
- Move logic out of pages when possible to keep them minimal

---

## ✅ **7. Environment Variables**

- Use `.env.local` for local secrets (never commit this)
- Prefix public variables with `NEXT_PUBLIC_`

---

## ✅ **8. Styling**

- Prefer **Tailwind CSS**, or **Styled Components**
- Avoid inline styles
- Use consistent class naming conventions (BEM, utility-first, etc.)

---

## ✅ **9. Error Handling**

- Always handle potential API and render errors
- Use `try/catch`, error boundaries, and fallback UI
- Log errors meaningfully

## ✅ **10. Code Splitting and Lazy Loading**

- Use `next/dynamic` for dynamic imports
- Avoid loading large components upfront unnecessarily

---

## ✅ **11. Linting & Formatting**

- Use **ESLint** with recommended rules and plugins:
- Use **Prettier** for consistent code formatting
- Add Git hooks with `husky` to enforce clean code on commit

---

## ✅ **12. Avoid Anti-Patterns**

- Don’t mix data fetching and rendering logic
- Don’t overuse context; prefer hooks and prop drilling where appropriate
- Avoid `any` in TypeScript

---

## ✅ **13. Use Next.js Features**

- Prefer `getServerSideProps`, `getStaticProps`, or `app/route.js` over custom fetch logic in components
- Use middleware for redirects, auth, etc.
- Use `Image` and `Link` components from `next/image` and `next/link`
