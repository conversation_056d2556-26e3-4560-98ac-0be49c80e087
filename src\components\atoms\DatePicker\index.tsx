import { CalendarOutlined } from '@ant-design/icons';
import { DatePicker as AntdDatePicker, DatePickerProps } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import { styled } from 'styled-components';

const StyledDatePicker = styled(AntdDatePicker)`
  box-shadow: var(--inner-box-shadow);

  .ant-picker-prefix {
    color: #858c90;
  }
` as typeof AntdDatePicker;

const DatePicker = (props: DatePickerProps) => {
  return (
    <StyledDatePicker
      suffixIcon={null}
      prefix={<CalendarOutlined />}
      {...props}
    />
  );
};

const RangePicker = styled((props: RangePickerProps) => {
  return (
    <AntdDatePicker.RangePicker
      suffixIcon={null}
      prefix={<CalendarOutlined />}
      {...props}
    />
  );
})`
  box-shadow: var(--inner-box-shadow);

  .ant-picker-prefix {
    color: #858c90;
  }
`;

export { DatePicker, RangePicker };
