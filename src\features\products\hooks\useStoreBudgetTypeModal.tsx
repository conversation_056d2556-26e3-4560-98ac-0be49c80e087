import { Text } from '@/components/atoms';
import { QueryKeysE } from '@/enums/query-keys';
import { CodeKbnE } from '@/features/sample-information/enums';
import { getMasterCode } from '@/services/common';
import { Radio as AntdRadio } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useQueries } from 'react-query';
import { getProductStoreBudgetTypes } from '../services';
import { ProductStoreBudgetI } from '../types';

export const useStoreBudgetTypeModal = (
  page: number,
  pageSize: number,
  setDefaultPage: () => void,
) => {
  const { t } = useTranslation('products');

  const results = useQueries([
    {
      queryKey: [QueryKeysE.PRODUCT_STORE_BUDGET_TYPES, page, pageSize],
      queryFn: () => getProductStoreBudgetTypes({ page, pageSize }),
      keepPreviousData: true,
    },
    {
      queryKey: [QueryKeysE.GENDER_CATEGORY],
      queryFn: () => getMasterCode({ code_kbn: [CodeKbnE.GENDER_CATEGORY] }),
      keepPreviousData: true,
    },
  ]);

  const storeBudgetQuery = results[0];
  const genderCategoryQuery = results[1];

  const applyFilters = () => {
    setDefaultPage();
  };

  const columns: ColumnsType<ProductStoreBudgetI> = useMemo(
    () => [
      {
        title: 'No',
        key: 'no',
        align: 'center',
        width: 60,
        render: (
          _: ProductStoreBudgetI,
          _record: ProductStoreBudgetI,
          index: number,
        ) => (page - 1) * pageSize + index + 1,
        className: 'first-column-bg',
      },
      {
        title: t('choise'),
        width: 120,
        key: 'choise',
        align: 'center',
        render: (_: unknown, record: ProductStoreBudgetI) => (
          <AntdRadio value={record.id} />
        ),
      },
      {
        title: t('gender'),
        width: 120,
        key: 'gender',
        align: 'center',
        render: (_: unknown, record: ProductStoreBudgetI) => (
          <Text className="text-left" variant="body2">
            {record.gender_category_name}
          </Text>
        ),
      },
      {
        title: t('nameStoreBudget'),
        key: 'code_name',
        align: 'center',
        render: (_: unknown, record: ProductStoreBudgetI) => (
          <Text className="text-left" variant="body2">
            {record.store_budget_type_name}
          </Text>
        ),
      },
    ],
    [page, pageSize, t],
  );

  return {
    storeBudgets: storeBudgetQuery.data?.data ?? [],
    genderCategories: genderCategoryQuery.data?.result ?? [],
    isLoading: storeBudgetQuery.isLoading || genderCategoryQuery.isLoading,
    isFetching: storeBudgetQuery.isFetching || genderCategoryQuery.isFetching,
    refetch: () => {
      storeBudgetQuery.refetch();
      genderCategoryQuery.refetch();
    },
    applyFilters,
    columns,
  };
};
