import {
  Checkbox as AntdCheckbox,
  CheckboxProps as AntdCheckboxProps,
} from 'antd';
import { styled } from 'styled-components';

const StyledCheckbox = styled(AntdCheckbox)`
  color: var(--main-text);

  .ant-checkbox-inner {
    box-shadow: var(--inner-box-shadow) !important;
    height: 21px;
    width: 21px;
    transition: border-color 0.2s;
  }

  .ant-checkbox-checked {
    .ant-checkbox-inner {
      background-color: var(--button-accent-text) !important;
      border-color: var(--button-accent-text) !important;
    }
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: var(--button-accent-text) !important;
    border-color: var(--button-accent-text) !important;
  }
` as typeof AntdCheckbox;

const StyledCheckboxGroup = styled(AntdCheckbox.Group)`
  color: var(--main-text);

  .ant-checkbox-inner {
    box-shadow: var(--inner-box-shadow) !important;
    height: 21px;
    width: 21px;
  }

  .ant-checkbox-checked {
    .ant-checkbox-inner {
      background-color: var(--button-accent-text) !important;
      border-color: var(--button-accent-text) !important;
    }
  }
` as typeof AntdCheckbox.Group;

export interface CheckboxPropsI extends AntdCheckboxProps {
  label?: string;
}

export const Checkbox = ({ label, ...props }: CheckboxPropsI) => {
  return (
    <StyledCheckbox {...props} className="align-middle">
      {label}
    </StyledCheckbox>
  );
};

Checkbox.Group = StyledCheckboxGroup;
