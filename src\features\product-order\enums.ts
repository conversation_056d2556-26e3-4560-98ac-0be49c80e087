export enum ProductOrderFormFieldKeyE {
  ID = 'id',
  NO = 'no',
  COLOR = 'color',
  SIZE = 'size',
  PRICE_TAX_EX = 'price_tax_ex',
  PRICE_TAX_IN = 'price_tax_in',
  FOB = 'fob',
  DELETE = 'delete',
  PMS_ORDER_SBT_CODE = 'pms_order_sbt_code',
  PMS_ORDER_DATETIME = 'pms_order_datetime',
  PMS_JODAI_TAX_OUT = 'pms_jodai_tax_out',
  PMS_JODAI_TAX_IN = 'pms_jodai_tax_in',
  PMS_ORDER_CALC_RATE = 'pms_order_calc_rate',
  COUNTRY_ORIGIN_CODE = 'country_origin_code',
  IMPORT_METHOD_CODE = 'import_method_code',
  IMPORT_COST_UNIT_CODE = 'import_cost_unit_code',
  PAYMENT_KGN = 'payment_kgn',
  PMS_MANAGER_CODE = 'pms_manager_code',
  SUPPLIER_FILTER = 'supplier_filter',
  SUPPLIER_FACTORY = 'supplier_pactory',
  PMS_SUPPLIER_CODE = 'pms_supplier_code',
  OPEN_SUPPLIER_MODEL = 'open_supplier_model',
  PMS_SUPPLIER_NAME = 'pms_supplier_name',
  PMS_SUPPLIER_FACTORY_CODE = 'pms_supplier_factory_code',
  INSPECT_PRE_FLG = 'inspect_pre_flg',
  INSPECT_PRE_STAFF_CODE = 'inspect_pre_staff_code',
  INSPECT_PRE_DATETIME = 'inspect_pre_datetime',
  INSPECT_PRE_REMARKS = 'inspect_pre_remarks',
  PART_NUMBER = 'part_number',
  PRODUCT_NAME = 'product_name',
  SELLING_POINT = 'selling_point',
  OTHER_NOTES = 'other_notes',
  COMMENT = 'comment',
  PROCESSING_DATE = 'processing_date',
  COMPANY_NAME = 'company_name',
  CONTRACT_DATE = 'contract_date',
  NOTE = 'note',
  CONVEY_MSG = 'convey_msg',
  PMS_PRODUCT_NO = 'pms_product_no',
  PMS_PRODUCT_NAME = 'pms_product_name',
  SELECT = 'select',
  PMS_JODAI_TAX_OUT_MODAL = 'pms_jodai_tax_out_modal',
  OPEN_PMS_JODAI_TAX_OUT_MODAL = 'open_pms_jodai_tax_out_modal',
}
