import { EditableTable } from '@/components/atoms';
import ImageUploadList from '@/components/organisms/ImageList';
import usePagination from '@/hooks/usePagination';
import { getServerSideTranslationsProps } from '@/utils/localization';
import type { TabsProps } from 'antd';
import { Tabs } from 'antd';
import { useState } from 'react';
import { ColumnDefinition } from 'react-tabulator';

const onChange = (key: string) => {
  console.info(key);
};

const Components = () => {
  const { pagination } = usePagination();

  const [data, setData] = useState([
    { id: 1, name: '<PERSON>', age: 29, email: '<EMAIL>' },
    { id: 2, name: '<PERSON>', age: 34, email: '<EMAIL>' },
    { id: 3, name: '<PERSON>', age: 22, email: '<EMAIL>' },
    { id: 4, name: '<PERSON>', age: 27, email: '<EMAIL>' },
    { id: 5, name: '<PERSON>', age: 31, email: 'mi<PERSON><PERSON>@example.com' },
    { id: 6, name: '<PERSON>', age: 25, email: '<EMAIL>' },
    { id: 7, name: '<PERSON>', age: 38, email: '<EMAIL>' },
    { id: 8, name: '<PERSON>', age: 33, email: '<EMAIL>' },
    { id: 9, name: 'Robert', age: 45, email: '<EMAIL>' },
    { id: 10, name: 'Anna', age: 29, email: '<EMAIL>' },
    { id: 11, name: 'James', age: 36, email: '<EMAIL>' },
    { id: 12, name: 'Emma', age: 24, email: '<EMAIL>' },
    { id: 13, name: 'William', age: 42, email: '<EMAIL>' },
    { id: 14, name: 'Olivia', age: 28, email: '<EMAIL>' },
    { id: 15, name: 'Daniel', age: 32, email: '<EMAIL>' },
    { id: 16, name: 'Sophia', age: 26, email: '<EMAIL>' },
    { id: 17, name: 'Matthew', age: 39, email: '<EMAIL>' },
    { id: 18, name: 'Isabella', age: 31, email: '<EMAIL>' },
    { id: 19, name: 'Joseph', age: 35, email: '<EMAIL>' },
    { id: 20, name: 'Mia', age: 27, email: '<EMAIL>' },
    { id: 21, name: 'Christopher', age: 44, email: '<EMAIL>' },
    { id: 22, name: 'Charlotte', age: 30, email: '<EMAIL>' },
    { id: 23, name: 'Andrew', age: 37, email: '<EMAIL>' },
    { id: 24, name: 'Ava', age: 25, email: '<EMAIL>' },
    { id: 25, name: 'Thomas', age: 41, email: '<EMAIL>' },
    { id: 26, name: 'Amelia', age: 28, email: '<EMAIL>' },
    { id: 27, name: 'Ryan', age: 33, email: '<EMAIL>' },
    { id: 28, name: 'Grace', age: 29, email: '<EMAIL>' },
    { id: 29, name: 'Nathan', age: 36, email: '<EMAIL>' },
    { id: 30, name: 'Victoria', age: 32, email: '<EMAIL>' },
  ]);

  const columns: ColumnDefinition[] = [
    { title: 'ID', field: 'id', width: 200 },
    { title: 'Name', field: 'name', editor: 'input' },
    { title: 'Age', field: 'age', editor: 'number' },
    {
      title: 'Email',
      field: 'email',
      editor: 'input',
      resizable: false,
    },
  ];

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Image List',
      children: <ImageUploadList />,
    },
    {
      key: '2',
      label: 'Table',
      children: (
        <EditableTable
          pagination={pagination}
          data={data}
          columns={columns}
          setData={setData}
        />
      ),
    },
    {
      key: '3',
      label: 'Tab 3',
      children: 'Content of Tab Pane 3',
    },
  ];

  return (
    <div className="p-10">
      <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
    </div>
  );
};

export const getServerSideProps = getServerSideTranslationsProps();

export default Components;
