import { useEffect, useState } from 'react';
import { ProductStoreBudgetI } from '../types';

export const useStoreBudgetSelector = (
  initialValue?: ProductStoreBudgetI,
  onChange?: (code: number) => void,
) => {
  const [selectedStore, selectedStoreSelected] =
    useState<ProductStoreBudgetI | null>(initialValue ?? null);

  useEffect(() => {
    if (initialValue) selectedStoreSelected(initialValue);
  }, [initialValue]);

  const handleSelectStore = (
    value: ProductStoreBudgetI,
    onClose?: () => void,
  ) => {
    selectedStoreSelected(value);
    onChange?.(value.id!);
    onClose?.();
  };

  return {
    selectedStore,
    selectedStoreSelected,
    handleSelectStore,
  };
};
