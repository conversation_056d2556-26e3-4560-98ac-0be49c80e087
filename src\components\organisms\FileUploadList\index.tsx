import { MAX_IMAGE_SIZE } from '@/constants/common';
import { MESSAGES } from '@/enums/messages';
import { uploadFiles } from '@/services/common';
import { updateFiles } from '@/services/fileService';
import { UploadFileResponseI } from '@/types/api';
import { DateHelper } from '@/utils/date';
import { Button, Checkbox, message, Table } from 'antd';
import { useTranslation } from 'next-i18next';
import React, { useEffect, useState } from 'react';

export interface UploadedFileI extends UploadFileResponseI {
  uploading?: boolean;
}

interface PropI {
  initialFiles: UploadedFileI[];
  maxFileSizeMB?: number;
  allowedTypes?: string[];
  urlUpdate: string;
}

const FileUploadList: React.FC<PropI> = ({
  initialFiles,
  maxFileSizeMB = MAX_IMAGE_SIZE,
  allowedTypes = [],
  urlUpdate,
}) => {
  const { t } = useTranslation('components');
  const [files, setFiles] = useState<UploadedFileI[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    setFiles(initialFiles || []);
    setSelectedKeys([]);
  }, [initialFiles]);

  const handleUpload = async (fileList: File[]) => {
    if (!fileList.length) return;

    setUploading(true);

    const tempFiles: UploadedFileI[] = fileList.map((file) => ({
      path: '',
      original_name: file.name,
      logical_name: file.name,
      uploading: true,
    }));

    setFiles((prev) => [...prev, ...tempFiles]);

    try {
      const uploadedFiles = await uploadFiles(fileList, 'document');

      const updatedFiles: UploadedFileI[] = uploadedFiles.map((f) => ({
        ...f,
        uploading: false,
      }));

      setFiles((prev) =>
        prev.map((file) => {
          const found = updatedFiles.find(
            (u) => u.original_name === file.original_name,
          );

          return found ? found : file;
        }),
      );

      message.success(t('form.uploadSuccess'));
    } catch (err: any) {
      message.error(err?.message || t('form.uploadFailed'));
      setFiles((prev) => prev.filter((f) => !f.uploading));
    } finally {
      setUploading(false);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target;
    if (!input.files) return;

    const filesArray = Array.from(input.files);

    const validFiles = filesArray.filter((file) => {
      if (!allowedTypes.includes(file.type)) {
        message.error(MESSAGES.E_INVALID_FILE_FORMAT);

        return false;
      }

      if (file.size / 1024 / 1024 > maxFileSizeMB) {
        message.error(MESSAGES.E_MAX_FILE_SIZE);

        return false;
      }

      return true;
    });

    handleUpload(validFiles);
    input.value = '';
  };

  const handleDelete = async () => {
    try {
      await updateFiles(files, selectedKeys, urlUpdate);
      message.success(t('form.updateSuccess'));
      setSelectedKeys([]);
    } catch (err: any) {
      message.error(err?.message || t('form.updateFailed'));
    }
  };

  const columns = [
    {
      title: 'No',
      render: (_record: any, _rowIndex: any, index: number) => index + 1,
      align: 'center' as const,
      width: 60,
    },
    {
      title: t('form.delete'),
      render: (_: any, record: UploadedFileI) => (
        <Checkbox
          disabled={record.uploading}
          checked={selectedKeys.includes(record.original_name)}
          onChange={(e) => {
            const checked = e.target.checked;
            setSelectedKeys((prev) =>
              checked
                ? [...prev, record.original_name]
                : prev.filter((key) => key !== record.original_name),
            );
          }}
        />
      ),
      align: 'center' as const,
      width: 80,
    },
    {
      title: t('form.nameFile'),
      render: (_: any, record: UploadedFileI) => (
        <div style={{ textAlign: 'left' }}>
          {record.uploading ? (
            <span className="text-gray-400 italic">
              {record.original_name} ({t('form.upLoading')})
            </span>
          ) : (
            <a
              href={record.path}
              className="text-blue-600 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              {record.logical_name || record.original_name}
            </a>
          )}
        </div>
      ),
      align: 'center' as const,
    },
    {
      title: t('form.dateAndTime'),
      render: () => {
        return (
          <div style={{ textAlign: 'center', whiteSpace: 'pre-line' }}>
            {`${DateHelper.formatJapaneseDateTime()}\n${t('form.maskedName')}`}
          </div>
        );
      },
      align: 'center' as const,
      width: 180,
    },
  ];

  return (
    <div>
      <div className="mb-4 border border-dashed border-gray-300 p-4 text-center">
        <p>{t('form.dropFile')}</p>
        <p>{t('form.or')}</p>

        <button
          type="button"
          onClick={() => document.getElementById('fileUploadInput')?.click()}
          className="mt-5 rounded border border-green-600 px-4 py-1 text-green-600 transition duration-200 hover:bg-green-100"
          disabled={uploading}
        >
          {t('form.addFiles')}
        </button>

        <input
          id="fileUploadInput"
          type="file"
          multiple
          accept={allowedTypes.join(',')}
          style={{ display: 'none' }}
          onChange={handleFileInputChange}
        />
      </div>

      <Table
        dataSource={files}
        columns={columns}
        rowKey="original_name"
        pagination={false}
        size="small"
        bordered
      />

      <div className="mt-4 flex gap-2">
        <Button danger onClick={handleDelete}>
          {t('form.deleteFiles')}
        </Button>
      </div>
    </div>
  );
};

export default FileUploadList;
