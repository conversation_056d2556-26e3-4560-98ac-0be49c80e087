import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { UploadOutlined } from '@ant-design/icons';
import { Meta, StoryObj } from '@storybook/react-vite';
import { Button } from './index';

const StorybookButton = withThemeProvider(Button);

const meta: Meta<typeof Button> = {
  title: 'Atoms/Button',
  component: StorybookButton,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'ボタンは、アクションをトリガーするために使用されるクリック可能な要素です。これらは、アプリのバックグラウンドまたはフォアグラウンドでアクションを初期化するために使用されます。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Button>;

export const Primary: StoryT = {
  args: {
    label: 'Button',
    type: 'primary',
  },
  parameters: {
    docs: {
      description: {
        story:
          'プライマリボタンは、状況におけるメインアクションに使用されます。セクションまたはビューには、プライマリボタンは1つだけであるべきです。',
      },
    },
  },
};

export const Secondary: StoryT = {
  args: {
    ...Primary.args,
    type: 'default',
  },
  parameters: {
    docs: {
      description: {
        story:
          'セカンダリボタンは、セカンダリアクションに使用されます。セクションまたはビューには複数のセカンダリボタンが存在できます。',
      },
    },
  },
};

export const SecondaryWithIcon: StoryT = {
  args: {
    ...Primary.args,
    type: 'default',
    label: 'Upload',
    icon: <UploadOutlined />,
  },
  parameters: {
    docs: {
      description: {
        story: 'セカンダリボタンにはアイコンも付けることができます。',
      },
    },
  },
};

export const Dashed: StoryT = {
  args: {
    ...Primary.args,
    type: 'dashed',
  },
  parameters: {
    docs: {
      description: {
        story:
          '破線ボタンは、目立たないアクションを示すため、またはリストに新しいアイテムを追加するために使用できます。',
      },
    },
  },
};

export const Text: StoryT = {
  args: {
    ...Primary.args,
    type: 'text',
  },
  parameters: {
    docs: {
      description: {
        story:
          'テキストボタンには境界線や背景がなく、非常に強調度の低いオプションです。',
      },
    },
  },
};

export const Link: StoryT = {
  args: {
    ...Primary.args,
    type: 'link',
  },
  parameters: {
    docs: {
      description: {
        story:
          'リンクボタンは、別のページまたはビューに移動するために使用されます。',
      },
    },
  },
};

export const WithPrefixIcon: StoryT = {
  args: {
    ...Primary.args,
    label: 'Upload',
    icon: <UploadOutlined />,
  },
  parameters: {
    docs: {
      description: {
        story:
          'ボタンには、アクションのより詳細なコンテキストを提供するためのアイコンを付けることができます。',
      },
    },
  },
};

export const WithSuffixIcon: StoryT = {
  args: {
    ...WithPrefixIcon.args,
    iconPosition: 'end',
  },
  parameters: {
    docs: {
      description: {
        story: 'アイコンはボタンテキストの最後に配置できます。',
      },
    },
  },
};

export const Loading: StoryT = {
  args: {
    ...Primary.args,
    loading: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          'ローディング状態は、アクションが進行中であることを示すために使用できます。',
      },
    },
  },
};
