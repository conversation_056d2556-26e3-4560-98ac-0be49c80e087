import { Button, Input, Text } from '@/components/atoms';
import { useTranslation } from 'react-i18next';

interface ProductCategoryPropsI {
  productNo: string;
  onAddCategory?: () => void;
}

export const ProductCategorySettingSection = ({
  productNo,
  onAddCategory,
}: ProductCategoryPropsI) => {
  const { t } = useTranslation('products');

  return (
    <div className="mb-6 flex flex-wrap items-center gap-4">
      <Button
        label={t('addProductCategory')}
        type="default"
        className="mb-0"
        onClick={onAddCategory}
      />
      <Text className="font-medium">{t('productCodeOrItemCode')}</Text>
      <div className="h-[32px] w-[220px]">
        <Input value={productNo} />
      </div>
      <Text className="font-medium">{t('copyProductCategorySettings')}</Text>
      <Button label={t('copy')} type="primary" className="mb-0" />
    </div>
  );
};
