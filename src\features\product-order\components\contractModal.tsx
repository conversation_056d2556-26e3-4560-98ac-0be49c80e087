import InformationUserCreateUpdate from '@/components/organisms/Footer/InformationUserCreateUpdate';
import { Form, FormFieldConfigI } from '@/components/organisms/Form';
import { SelectE } from '@/enums/select';
import { DateHelper } from '@/utils/date';
import { Form as AntdForm, Modal } from 'antd';
import { useTranslation } from 'next-i18next';
import { useEffect, useMemo } from 'react';
import { ProductOrderFormFieldKeyE } from '../../product-order/enums';
import { ProductOrderDetailI } from '../types';

interface ContractModalPropsI {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ProductOrderDetailI['contracts'][number]) => void;
  type?: 'create' | 'edit';
  initialValues: ProductOrderDetailI['contracts'][number];
}

export default function ContractModal({
  isOpen,
  onClose,
  onSubmit,
  type = 'edit',
  initialValues,
}: ContractModalPropsI) {
  const { t } = useTranslation(['product-order', 'common']);
  const [contractForm] = AntdForm.useForm();

  const contractFormFields: FormFieldConfigI[] = [
    {
      name: ProductOrderFormFieldKeyE.COMPANY_NAME,
      type: 'select',
      label: t('companyName'),
      apiConfig: {
        params: { code_kbn: [SelectE.M_CODE_DROPDOWN_EXAMPLE], hidden_flg: 0 },
      },
      rules: [
        { required: true, message: t('common:pleaseEnterRequiredFields') },
      ],
    },
    {
      name: ProductOrderFormFieldKeyE.CONTRACT_DATE,
      type: 'datepicker',
      label: t('contractDate'),
    },
    {
      name: ProductOrderFormFieldKeyE.NOTE,
      type: 'textarea',
      rows: 4,
      label: t('common:notes'),
    },
  ];

  const getFormInitialValues = useMemo(() => {
    if (type === 'edit') {
      return {
        ...initialValues,
        contract_date: initialValues?.contract_date
          ? DateHelper.toDayjs(initialValues?.contract_date)
          : null,
      };
    }

    return Object.values(ProductOrderFormFieldKeyE).reduce(
      (acc, key) => {
        acc[key] = null;

        return acc;
      },
      {} as Record<string, null>,
    );
  }, [type, initialValues]);

  useEffect(() => {
    if (isOpen) {
      contractForm.setFieldsValue(getFormInitialValues);
    }
  }, [contractForm, isOpen, getFormInitialValues]);

  const handleFormDelete = () => {
    if (type === 'edit') {
      //logic to delete the contract
      onClose();
    }
  };

  const handleFormSubmit = () => {
    onSubmit(contractForm.getFieldsValue());
    onClose();
  };

  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={900}
      title={t('contractDetails')}
    >
      <Form
        name="contract-form"
        formFields={contractFormFields}
        form={contractForm}
        style={{ marginTop: 30 }}
      />
      <InformationUserCreateUpdate
        type={type}
        handleSubmit={() => handleFormSubmit()}
        handleDelete={() => handleFormDelete()}
        data={initialValues}
      />
    </Modal>
  );
}
