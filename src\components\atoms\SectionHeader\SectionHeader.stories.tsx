import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { SectionHeader } from './index';

const ThemedSectionHeader = withThemeProvider(SectionHeader);

const meta: Meta<typeof ThemedSectionHeader> = {
  title: 'Atoms/SectionHeader',
  component: ThemedSectionHeader,
  tags: ['autodocs'],
  argTypes: {
    title: { control: 'text' },
    className: { control: 'text' },
  },
  parameters: {
    docs: {
      description: {
        component:
          'SectionHeaderコンポーネントは、装飾的な線とともにタイトルを表示するために使用され、一般的にセクションの見出しに使用されます。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof SectionHeader>;

export const Default: StoryT = {
  args: {
    title: '顧客情報',
  },
};
