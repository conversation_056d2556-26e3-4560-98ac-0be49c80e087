import { apiRequest, apiRequestMock } from '@/api/api-request';
import { ApiEndpointE } from '@/enums';
import { ApiResponseListI } from '@/types/api';
import { OrderInformationI, ProductDetailI, StoreBudgetTypeI } from './types';

export const getStoreBudgetTypes = async (): Promise<
  ApiResponseListI<StoreBudgetTypeI>['result']
> => {
  const response = await apiRequestMock.get(
    `${ApiEndpointE.STORE_BUDGET_TYPES}`,
  );

  return response.data.result;
};

export const getMasterCodeMock = async (params: { code_kbn: string[] }) => {
  const response = await apiRequestMock.get(ApiEndpointE.MASTER_CODE, {
    params,
  });

  return response.data;
};

export const getProduct = async (id: number): Promise<ProductDetailI> => {
  const response = await apiRequestMock.get(`${ApiEndpointE.PRODUCTS}/${id}`);

  return response.data.data;
};

export const createProduct = (payload: ProductDetailI) => {
  return apiRequest.post(ApiEndpointE.PRODUCT_CREATE, payload);
};

export const updateProduct = (id: number, payload: ProductDetailI) => {
  return apiRequest.patch(`${ApiEndpointE.PRODUCTS}/${id}`, payload);
};

export const getOrderInformation = async (params: {
  page: number;
  per_page: number;
  id?: string;
}): Promise<ApiResponseListI<OrderInformationI>['result']> => {
  const response = await apiRequestMock.get(
    `${ApiEndpointE.ORDER_INFORMATION}`,
    { params },
  );

  return response.data.result;
};

export const getProductStoreBudgetTypes = async (params: {
  page: number;
  pageSize: number;
}) => {
  const response = await apiRequestMock.get(
    ApiEndpointE.PRODUCT_STORE_BUDGET_TYPES,
    {
      params,
    },
  );

  return response.data.result;
};
