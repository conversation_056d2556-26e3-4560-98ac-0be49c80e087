import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { Meta, StoryObj } from '@storybook/react-vite';
import { Button } from '../Button';
import { Notification } from './index';

const ThemedButton = withThemeProvider(Button);

const meta: Meta = {
  title: 'Atoms/Notification',
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          '通知は、ユーザーに簡潔でタイムリーかつ関連性の高い情報を邪魔にならない方法で表示するために使用されます。通常、画面の隅に表示され、アクションに関するフィードバックを提供したり、イベントをユーザーに警告したり、システムメッセージを表示したりするために使用できます。',
      },
    },
  },
};

export default meta;

export const Default: StoryObj = {
  render: () => {
    const [api, contextHolder] = Notification.useNotification();

    const openNotification = () => {
      api.info({
        message: 'Notification Title',
        description:
          'This is the content of the notification. This is the content of the notification. This is the content of the notification.',
        placement: 'topRight',
      });
    };

    return (
      <>
        {contextHolder}
        <ThemedButton
          type="primary"
          onClick={openNotification}
          label="Open the notification"
        />
      </>
    );
  },
  parameters: {
    docs: {
      description: {
        story: '標準的な通知メッセージ。',
      },
    },
  },
};

export const Error: StoryObj = {
  render: () => {
    const [api, contextHolder] = Notification.useNotification();

    const openNotification = () => {
      api.error({
        message: 'Notification Title',
        description:
          'This is the content of the notification. This is the content of the notification. This is the content of the notification.',
        placement: 'topRight',
      });
    };

    return (
      <>
        {contextHolder}
        <ThemedButton
          type="primary"
          onClick={openNotification}
          label="Open the notification"
        />
      </>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'エラーが発生したことを示す通知。',
      },
    },
  },
};

export const Warning: StoryObj = {
  render: () => {
    const [api, contextHolder] = Notification.useNotification();

    const openNotification = () => {
      api.warning({
        message: 'Notification Title',
        description:
          'This is the content of the notification. This is the content of the notification. This is the content of the notification.',
        placement: 'topRight',
      });
    };

    return (
      <>
        {contextHolder}
        <ThemedButton
          type="primary"
          onClick={openNotification}
          label="Open the notification"
        />
      </>
    );
  },
  parameters: {
    docs: {
      description: {
        story: '潜在的な問題についてユーザーに警告する通知。',
      },
    },
  },
};
