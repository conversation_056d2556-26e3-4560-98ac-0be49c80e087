import { SelectE } from '@/enums/select';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method === 'GET') {
    await new Promise((resolve) => setTimeout(resolve, 400));

    const dataColors = [
      {
        id: 1,
        code_kbn: SelectE.M_CODE_COLOR,
        code: 'BLACK',
        code_name: '<PERSON><PERSON><PERSON><PERSON>',
        code_name_short: 'BUYER1',
        code_name_alpha: 'maxime',
        code_name_alpha_short: 'eligendi',
        sort_key: 17,
        hidden_flg: 0,
        code_remarks: null,
        code_value_01: 'ipsa',
        code_value_02: 'laboriosam',
        code_value_03: 'sequi',
        code_value_04: null,
        code_value_05: 'ab',
        code_value_06: null,
        code_value_07: null,
        code_value_08: null,
        code_value_09: null,
        code_value_10: 'non',
        code_value_11: 'fugit',
        code_value_12: null,
        code_value_13: null,
        code_value_14: null,
        code_value_15: null,
        code_value_16: null,
        code_value_17: 'quos',
        code_value_18: null,
        code_value_19: 'esse',
        code_value_20: null,
        deleted_at: null,
        created_at: '1991-05-21T21:24:04.000000Z',
        created_user_id: 'kmaggio',
        created_program_id: 'PRG951',
        updated_at: '2019-08-27T13:27:02.000000Z',
        updated_user_id: 'hyatt.dell',
        updated_program_id: 'PRG389',
      },
      {
        id: 2,
        code_kbn: SelectE.M_CODE_COLOR,
        code: 'WHITE',
        code_name: 'WHITE',
        code_name_short: 'BUYER2',
        code_name_alpha: 'animi',
        code_name_alpha_short: 'aspernatur',
        sort_key: 97,
        hidden_flg: 0,
        code_remarks: null,
        code_value_01: null,
        code_value_02: null,
        code_value_03: 'sed',
        code_value_04: null,
        code_value_05: 'similique',
        code_value_06: 'fugit',
        code_value_07: null,
        code_value_08: 'sit',
        code_value_09: null,
        code_value_10: null,
        code_value_11: 'sit',
        code_value_12: 'expedita',
        code_value_13: 'temporibus',
        code_value_14: null,
        code_value_15: 'quam',
        code_value_16: 'et',
        code_value_17: null,
        code_value_18: 'qui',
        code_value_19: null,
        code_value_20: null,
        deleted_at: null,
        created_at: '1981-07-03T00:31:25.000000Z',
        created_user_id: 'harber.rahsaan',
        created_program_id: 'PRG636',
        updated_at: '2017-12-03T23:11:19.000000Z',
        updated_user_id: 'lafayette54',
        updated_program_id: 'PRG686',
      },
    ];
    const dataSizes = [
      {
        id: 1,
        code_kbn: SelectE.M_CODE_SIZE,
        code: 'S',
        code_name: 'S',
        code_name_short: 'BUYER1',
        code_name_alpha: 'maxime',
        code_name_alpha_short: 'eligendi',
        sort_key: 17,
        hidden_flg: 0,
        code_remarks: null,
        code_value_01: 'ipsa',
        code_value_02: 'laboriosam',
        code_value_03: 'sequi',
        code_value_04: null,
        code_value_05: 'ab',
        code_value_06: null,
        code_value_07: null,
        code_value_08: null,
        code_value_09: null,
        code_value_10: 'non',
        code_value_11: 'fugit',
        code_value_12: null,
        code_value_13: null,
        code_value_14: null,
        code_value_15: null,
        code_value_16: null,
        code_value_17: 'quos',
        code_value_18: null,
        code_value_19: 'esse',
        code_value_20: null,
        deleted_at: null,
        created_at: '1991-05-21T21:24:04.000000Z',
        created_user_id: 'kmaggio',
        created_program_id: 'PRG951',
        updated_at: '2019-08-27T13:27:02.000000Z',
        updated_user_id: 'hyatt.dell',
        updated_program_id: 'PRG389',
      },
      {
        id: 2,
        code_kbn: SelectE.M_CODE_SIZE,
        code: 'M',
        code_name: 'M',
        code_name_short: 'BUYER2',
        code_name_alpha: 'animi',
        code_name_alpha_short: 'aspernatur',
        sort_key: 97,
        hidden_flg: 0,
        code_remarks: null,
        code_value_01: null,
        code_value_02: null,
        code_value_03: 'sed',
        code_value_04: null,
        code_value_05: 'similique',
        code_value_06: 'fugit',
        code_value_07: null,
        code_value_08: 'sit',
        code_value_09: null,
        code_value_10: null,
        code_value_11: 'sit',
        code_value_12: 'expedita',
        code_value_13: 'temporibus',
        code_value_14: null,
        code_value_15: 'quam',
        code_value_16: 'et',
        code_value_17: null,
        code_value_18: 'qui',
        code_value_19: null,
        code_value_20: null,
        deleted_at: null,
        created_at: '1981-07-03T00:31:25.000000Z',
        created_user_id: 'harber.rahsaan',
        created_program_id: 'PRG636',
        updated_at: '2017-12-03T23:11:19.000000Z',
        updated_user_id: 'lafayette54',
        updated_program_id: 'PRG686',
      },
      {
        id: 3,
        code_kbn: SelectE.M_CODE_SIZE,
        code: 'L',
        code_name: 'L',
        code_name_short: 'BUYER2',
        code_name_alpha: 'animi',
        code_name_alpha_short: 'aspernatur',
        sort_key: 97,
        hidden_flg: 0,
        code_remarks: null,
        code_value_01: null,
        code_value_02: null,
        code_value_03: 'sed',
        code_value_04: null,
        code_value_05: 'similique',
        code_value_06: 'fugit',
        code_value_07: null,
        code_value_08: 'sit',
        code_value_09: null,
        code_value_10: null,
        code_value_11: 'sit',
        code_value_12: 'expedita',
        code_value_13: 'temporibus',
        code_value_14: null,
        code_value_15: 'quam',
        code_value_16: 'et',
        code_value_17: null,
        code_value_18: 'qui',
        code_value_19: null,
        code_value_20: null,
        deleted_at: null,
        created_at: '1981-07-03T00:31:25.000000Z',
        created_user_id: 'harber.rahsaan',
        created_program_id: 'PRG636',
        updated_at: '2017-12-03T23:11:19.000000Z',
        updated_user_id: 'lafayette54',
        updated_program_id: 'PRG686',
      },
    ];
    res.status(200).json({
      status: 200,
      result:
        req.query['code_kbn[]'] === SelectE.M_CODE_COLOR
          ? dataColors
          : dataSizes,
      message: 'データの取得に成功しました。',
    });
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
