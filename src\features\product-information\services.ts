import { apiRequestMock } from '@/api/api-request';
import { ApiEndpointE, PaginationParamsE } from '@/enums';
import { ApiResponseListI } from '@/types/api';
import { DateHelper } from '@/utils';
import { ProductFormFieldKeyE } from './enums';
import {
  ProductClassificationDataI,
  ProductI,
  ProductSearchFormValuesI,
} from './types';

const buildProductQueryParams = (
  page: number,
  perPage: number,
  filters: ProductSearchFormValuesI,
): URLSearchParams => {
  const params = new URLSearchParams();

  const multiValueFields = [
    ProductFormFieldKeyE.PLANNER,
    ProductFormFieldKeyE.PRODUCT_CATEGORY,
    ProductFormFieldKeyE.GENDER_CATEGORY,
    ProductFormFieldKeyE.COLOR,
    ProductFormFieldKeyE.SIZE,
    ProductFormFieldKeyE.ORDER_TYPE,
  ];

  const dateValueFields = [
    ProductFormFieldKeyE.LOCAL_SHIPPING_PLAN_DATETIME_FROM,
    ProductFormFieldKeyE.LOCAL_SHIPPING_PLAN_DATETIME_TO,
    ProductFormFieldKeyE.LOCAL_SHIPPING_ACTUAL_DATETIME_FROM,
    ProductFormFieldKeyE.LOCAL_SHIPPING_ACTUAL_DATETIME_TO,
    ProductFormFieldKeyE.STORE_ARRIVAL_PLAN_DATETIME_FROM,
    ProductFormFieldKeyE.STORE_ARRIVAL_PLAN_DATETIME_TO,
    ProductFormFieldKeyE.STORE_ARRIVAL_RESULT_DATETIME_FROM,
    ProductFormFieldKeyE.STORE_ARRIVAL_RESULT_DATETIME_TO,
  ];

  for (const [key, value] of Object.entries(filters)) {
    if (value === undefined || value === null || value === '') {
      continue;
    }

    const isMultiValue =
      multiValueFields.includes(key as ProductFormFieldKeyE) &&
      Array.isArray(value);

    if (isMultiValue) {
      for (const v of value) {
        params.append(`${key}[]`, String(v));
      }
      continue;
    }

    if (dateValueFields.includes(key as ProductFormFieldKeyE)) {
      const formatted = DateHelper.formatDate({
        date: value,
        format: DateHelper.FORMAT.FORMAT_31,
      });

      if (formatted) {
        params.append(key, formatted);
      }
      continue;
    }

    params.append(key, String(value.trim()));
  }

  params.append(PaginationParamsE.PAGE, String(page));
  params.append(PaginationParamsE.PER_PAGE, String(perPage));

  return params;
};

export const getProducts = async (
  page: number,
  perPage: number,
  filters: ProductSearchFormValuesI,
): Promise<ApiResponseListI<ProductI>['result']> => {
  const params = buildProductQueryParams(page, perPage, filters);
  const response = await apiRequestMock.get(ApiEndpointE.PRODUCTS, {
    params,
  });

  return response.data.result;
};

export const getProductClassifications = async (
  page: number,
  perPage: number,
  filters: ProductSearchFormValuesI,
): Promise<ApiResponseListI<ProductClassificationDataI>['result']> => {
  const params = buildProductQueryParams(page, perPage, filters);
  const response = await apiRequestMock.get(
    ApiEndpointE.PRODUCT_CLASSIFICATION,
    {
      params,
    },
  );

  return response.data.result;
};
