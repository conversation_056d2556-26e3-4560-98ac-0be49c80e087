import { Input } from '@/components/atoms';
import {
  ExperimentOutlined,
  LaptopOutlined,
  MenuOutlined,
  SearchOutlined,
  SettingOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { Menu, MenuProps } from 'antd';
import { MenuItemType, SubMenuType } from 'antd/es/menu/interface';
import { useTranslation } from 'next-i18next';
import { usePathname, useRouter } from 'next/navigation';
import { useMemo } from 'react';
import styled from 'styled-components';

const StyledMenu = styled(Menu)`
  .ant-menu-item {
    /* background-color: #515055 !important; */
    /* margin-inline: auto !important; */
    /* z-index: 100 !important; */
  }

  .ant-menu-submenu-title {
    /* background-color: #151519 !important; */
  }

  .ant-menu-submenu {
    /* background-color: #151519 !important; */
  }

  .ant-menu-submenu {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
    border-bottom: 1px solid #29282d !important;
    border-radius: 0px !important;
  }

  .ant-menu .ant-menu-item-selected {
    .ant-menu-title-content {
      font-family: NotoSansJP;
      color: var(--button-accent-text) !important;
    }
  }

  .ant-menu-title-content {
    font-family: NotoSansJP;
    /* color: white !important; */
  }

  .ant-menu-submenu-open {
    background-color: #151519;
    padding-left: 16px;
    padding-right: 16px;
    border-radius: 0px !important;
  }

  .ant-menu-item.ant-menu-item-only-child {
    color: white;
    background: rgba(255, 255, 255, 0.2) 0% 0% no-repeat padding-box;
  }

  .ant-menu-submenu-selected.ant-menu-submenu-open > .ant-menu-submenu-title {
    color: white;
  }
`;

interface SidebarPropsI {
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
}

const Sidebar = ({ collapsed, setCollapsed }: SidebarPropsI) => {
  const pathname = usePathname();
  const router = useRouter();
  const { t } = useTranslation('components');

  const menuItems: MenuProps['items'] = [
    {
      key: '/budget',
      icon: <ExperimentOutlined className="!text-[18px]" />,
      label: t('sidebar.budgetManagement'),
    },
    {
      key: '/production-management',
      icon: <SearchOutlined className="!text-[18px]" />,
      label: t('sidebar.productionManagement'),
      children: [
        { key: '/samples', label: t('sidebar.sampleInformationList') },
        { key: '/products', label: t('sidebar.productInformationList') },
      ],
    },
    {
      key: '/order',
      icon: <TeamOutlined className="!text-[18px]" />,
      label: t('sidebar.orderManagement'),
    },
    {
      key: '/inventory',
      icon: <LaptopOutlined className="!text-[18px]" />,
      label: t('sidebar.inventoryManagement'),
    },
    {
      key: '/product-management',
      icon: <SettingOutlined className="!text-[18px]" />,
      label: t('sidebar.productManagement'),
    },
  ].map((menu) => ({
    ...menu,
    onClick: (e) => {
      router.push(e.key);
    },
  }));

  const defaultOpenKeys = useMemo(() => {
    let defaultOpenKeys: string[] = [];

    for (const menu of menuItems as SubMenuType<MenuItemType>[]) {
      if (menu?.key === pathname) {
        defaultOpenKeys = [menu?.key];

        break;
      }

      if (menu?.children?.length) {
        for (const child of menu.children) {
          if (child?.key === pathname) {
            defaultOpenKeys = [menu.key];

            break;
          }
        }
      }
    }

    return defaultOpenKeys;
  }, [pathname, menuItems]);

  return (
    <div className="flex h-full flex-col justify-between bg-[#515055] !text-white">
      <div className="">
        <div
          className="ml-[3px] cursor-pointer !px-5 !py-[15.5px]"
          onClick={() => setCollapsed(!collapsed)}
        >
          <MenuOutlined className="text-[30px]" />
        </div>

        <StyledMenu
          mode="inline"
          inlineCollapsed={collapsed}
          style={{ borderRight: 0, backgroundColor: '#515055' }}
          theme="dark"
          items={menuItems}
          defaultSelectedKeys={[pathname]}
          selectedKeys={[pathname]}
          defaultOpenKeys={defaultOpenKeys}
        />
      </div>

      <div className="p-4">
        {!collapsed && (
          <Input
            placeholder={t('sidebar.searchPlaceholder')}
            suffix={<SearchOutlined />}
            style={{
              borderRadius: '5px',
              opacity: 0.4,
            }}
          />
        )}
      </div>
    </div>
  );
};

export default Sidebar;
