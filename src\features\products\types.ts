export interface StoreBudgetTypeI {
  id: number;
  t_pms_product_fkid?: number;
  pms_color_code: string;
  pms_color?: {
    code_kbn: string;
    code: string;
    code_name: string;
  };
  pms_size_code: string;
  pms_size?: string | null | number;
  orders?: boolean[];
  isRegistered: boolean;
}

export interface ProductDetailI {
  id: number;
  pms_product_seq_no: number;
  pms_product_no: string;
  corporate_id: string;
  pms_product_name: string;

  gender_category_code: string;
  gender_category: {
    code_kbn: string;
    code: string;
    code_name: string;
  };

  product_category_code: string;
  product_category: {
    code_kbn: string;
    code: string;
    code_name: string;
  };

  m_store_budget_type_fkid: number;
  store_budget_type: {
    id: number;
    gender_category_code: string;
    store_budget_type_code: string;
    store_budget_type_name: string;
  };

  pms_sales_plan_start_datetime: string;
  pms_sales_plan_end_datetime: string;
  pms_store_withdraw_datetime: string;

  pms_sales_season_code: string | null;
  pms_sample_no: string;
  pms_planner_code: string;
  pms_hs_code: string | null;
  pms_remarks: string;

  t_product_types: TProductTypeI[];
  created_at?: string;
  updated_at?: string;
  created_by?: {
    id: number;
    name: string;
  };
  updated_by?: {
    id: number;
    name: string;
  };
}

export interface ProductCategoryI {
  code_kbn: string;
  code: string;
  code_name: string;
}

export interface ProductCategoryDataI extends ProductCategoryI {
  id: string;
}

export interface PmsShippingI {
  id: number | string;
  t_pms_product_fkid: number;
  pms_order_cnt: number;
  pms_shipping_cnt: number;
  local_shipping_plan_datetime: string;
  local_shipping_plan_week_no_n: number | null;
  local_shipping_result_datetime: string | null;
  local_shipping_result_week_no_n: number;
  store_arrival_plan_datetime: string;
  store_arrival_plan_week_no_n: string | null;
  store_arrival_result_datetime: string | null;
  store_arrival_result_week_no_n: string | null;
  country_origin_code: string;
  payment_plan_ym: string;
  fec_no: string;
  fec_rate: number | null;
  fec_reserve_datetime: string | null;
  fec_use_start_datetime: string | null;
  fec_use_end_datetime: string | null;
  number_check_flg: number;
  shipping_problem_flg: number;
  pms_mgmt_tbl_exclude_flg: number;
  pms_stats_exclude_flg: number;
  pms_supplier_stats_exclude_flg: number;
  delivery_complete_flg: number;
}

export interface OrderInformationBaseI {
  id: number | string;
  t_pms_product_fkid: number;
  pms_order_cnt: number;
  pms_order_sbt_code: string;
  pms_order_datetime: string | null;
  pms_jodai_tax_out: string;
  pms_jodai_tax_in: string;
  pms_order_calc_rate: number;
  country_origin_code: string;
  import_method_code: string;
  import_cost_unit_code: string;
  payment_kgn: string;
  pms_manager_code: string;
  pms_supplier_code: string;
  inspect_pre_flg: number;
  inspect_pre_datetime: string | null;
  inspect_pre_staff_code: string;
  inspect_pre_note: string | null;
  media_file_path?: string;
}

export interface OrderInformationI extends OrderInformationBaseI {
  pms_shippings: Array<PmsShippingI>;
}

export interface OrderInformationDataTableI extends OrderInformationBaseI {
  shipping?: PmsShippingI;
  numberOfShipments?: number;
  idx: number;
  isFirst?: boolean;
}

export interface ProductTypeGroupI {
  id: number;
  pms_product_type_group_name: string;
  corporate_id: number;
  sort_key: number;
  hidden_flg: number;
}

export interface ProductTypeI {
  id: number;
  pms_product_type_name: string;
  corporate_id: number;
  m_pms_product_type_group_fkid: number;
  product_type_group: ProductTypeGroupI;
  sort_key: number;
  hidden_flg: number;
}

export interface TProductTypeI {
  id: number;
  t_pms_product_fkid: number;
  m_pms_product_type_fkid: number;
  product_type_fkid: ProductTypeI;
  is_checked?: boolean;
}

export interface ProductStoreBudgetI {
  id?: number;
  gender_category_name?: string;
  gender_category_code?: string;
  store_budget_type_code?: string;
  store_budget_type_name?: string;
}
