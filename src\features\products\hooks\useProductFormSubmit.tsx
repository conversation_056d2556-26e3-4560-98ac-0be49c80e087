import { handleConfirmSubmit } from '@/utils';
import { message } from 'antd';
import { FormInstance } from 'antd/es/form';
import { useTranslation } from 'react-i18next';
import { createProduct, updateProduct } from '../../products/services';
import {
  ProductDetailI,
  StoreBudgetTypeI,
  TProductTypeI,
} from '../../products/types';

export const useProductFormSubmit = ({
  type,
  product,
  productTypes,
}: {
  type: 'create' | 'edit';
  product?: ProductDetailI;
  productTypes: TProductTypeI[];
}) => {
  const { t } = useTranslation('products');

  const handleSubmit = async (
    basicForm: FormInstance,
    dataTableColor: StoreBudgetTypeI[],
  ) => {
    try {
      const isValidColor = !!dataTableColor.find(
        (item) => !item.pms_color_code,
      );
      const isValidSize = !!dataTableColor.find((item) => !item.pms_size_code);

      const [basicValues] = await Promise.all([basicForm.validateFields()]);

      const allParams = {
        ...basicValues,
        t_product_types: productTypes,
      };

      if ((isValidColor || isValidSize) && type === 'edit') {
        if (isValidColor) {
          message.error(t('colorRequiredFields'));
        }

        if (isValidSize) {
          message.error(t('sizeRequiredFields'));
        }

        return;
      }
      const confirm = await handleConfirmSubmit(t('askUpdate'));
      if (!confirm) return;

      if (type === 'create') {
        await createProduct(allParams);

        return;
      }

      if (product?.id) {
        await updateProduct(product.id, allParams);
      }
    } catch {
      message.error('保存に失敗しました');
    }
  };

  return { handleSubmit };
};
