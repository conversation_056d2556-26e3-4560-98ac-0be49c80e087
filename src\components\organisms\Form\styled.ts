import { Button } from 'antd';
import styled from 'styled-components';

export const StyledFormContainer = styled.div`
  .ant-form {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
  }

  .ant-col {
    border-right: 1px solid #d9d9d9;
    &:last-child {
      border-right: none;
    }
  }

  .ant-row {
    border-bottom: 1px solid #d9d9d9;
    &:last-child {
      border-bottom: none;
    }
  }

  .ant-form-item-row {
    width: 100% !important;
  }
`;

export const StyledFormField = styled.div`
  display: flex;
  height: 100%;

  .ant-form-item {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 0;
    height: 100%;
    align-items: center;
  }
`;

export const StyledFieldLabel = styled.div<{
  $labelColSpan?: number;
  $labelPosition?: 'left' | 'right' | 'center';
}>`
  position: relative;
  background-color: var(--highlight-outline);
  padding: 8px 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  border-right: 1px solid #d9d9d9;
  flex-shrink: 0;

  justify-content: ${({ $labelPosition = 'center' }) =>
    $labelPosition === 'left'
      ? 'flex-start'
      : $labelPosition === 'right'
        ? 'flex-end'
        : 'center'};

  text-align: ${({ $labelPosition = 'center' }) => $labelPosition};

  ${({ $labelColSpan }) =>
    $labelColSpan
      ? `flex-basis: ${($labelColSpan / 24) * 100}%;`
      : 'width: 150px;'}
`;

export const StyledFieldInput = styled.div`
  padding: 8px 12px;
  flex: 1;
  background-color: #fff;

  .ant-input-outlined {
    border: 1px solid #bbbbbb;
    &:hover {
      border-color: #5fd7a2;
    }
  }
`;

export const StyledFormActions = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;

  .ant-btn {
    min-width: 120px;
  }
`;

export const StyledButton = styled(Button)`
  &.ant-btn-primary {
    background-color: #00a7b8;
    border-color: #00a7b8;
    &:hover {
      background-color: #0093a2;
      border-color: #0093a2;
    }
  }
  &.ant-btn-default {
    color: #00a7b8;
    border-color: #00a7b8;
    &:hover {
      color: #0093a2;
      border-color: #0093a2;
    }
  }
`;
