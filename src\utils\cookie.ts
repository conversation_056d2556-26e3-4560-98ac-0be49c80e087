export interface CookieOptionsI {
  path?: string;
  expires?: Date | string | number;
  maxAge?: number;
  domain?: string;
  secure?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
}

export class CookieUtil {
  /**
   * Sets a cookie with optional options.
   */
  static set(name: string, value: string, options: CookieOptionsI = {}): void {
    let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;

    if (options.expires) {
      const expires =
        typeof options.expires === 'number'
          ? new Date(Date.now() + options.expires * 1000)
          : new Date(options.expires);
      cookieString += `; expires=${expires.toUTCString()}`;
    }

    if (options.maxAge) cookieString += `; max-age=${options.maxAge}`;
    if (options.path) cookieString += `; path=${options.path}`;
    if (options.domain) cookieString += `; domain=${options.domain}`;
    if (options.secure) cookieString += `; secure`;
    if (options.sameSite) cookieString += `; samesite=${options.sameSite}`;

    document.cookie = cookieString;
  }

  /**
   * Gets a cookie by name.
   */
  static get(name: string): string | null {
    const cookies = document.cookie.split('; ');
    for (const cookie of cookies) {
      const [key, ...rest] = cookie.split('=');
      if (decodeURIComponent(key) === name) {
        return decodeURIComponent(rest.join('='));
      }
    }

    return null;
  }

  /**
   * Deletes a cookie by setting its expiration in the past.
   */
  static delete(name: string, path = '/'): void {
    this.set(name, '', { path, expires: new Date(0) });
  }

  /**
   * Clears all cookies for the current path and domain.
   */
  static clear(path = '/'): void {
    const cookies = document.cookie.split('; ');
    for (const cookie of cookies) {
      const [key] = cookie.split('=');
      if (key) {
        this.delete(decodeURIComponent(key.trim()), path);
      }
    }
  }
}
