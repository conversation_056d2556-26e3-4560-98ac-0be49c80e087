import { Text } from '@/components/atoms';

interface ViewTablePropsI {
  data: Array<{ label: string; value: string }>;
}

const ViewTable = ({ data }: ViewTablePropsI) => {
  return (
    <div className="flex w-full flex-col divide-y divide-[#BBBBBB] overflow-hidden rounded-md border border-[#BBBBBB]">
      {data.map((item, idx) => (
        <div
          key={idx}
          className="flex w-full flex-row divide-x divide-[#BBBBBB]"
        >
          <div
            className={`flex w-[150px] min-w-[150px] grow-0 items-center justify-end bg-[#F6F6F6] px-3 py-2 font-semibold`}
          >
            <Text variant="span" className="max-w-full truncate">
              {item.label}
            </Text>
          </div>
          <div className="relative grow bg-white">
            <div className="absolute inset-0 flex h-full w-full items-center justify-start px-3 py-2">
              <Text variant="span" className="max-w-full truncate">
                {item.value}
              </Text>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ViewTable;
