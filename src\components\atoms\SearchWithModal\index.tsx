import { Input, Text } from '@/components/atoms';
import { openModal } from '@/utils';
import { SearchOutlined } from '@ant-design/icons';
import React from 'react';

interface SearchWithModalPropsI {
  content: (props: { onClose: () => void }) => React.ReactNode;
  label?: string;
  title?: string;
  textSearch?: string;
}

const SearchWithModal: React.FC<SearchWithModalPropsI> = ({
  content,
  label = '',
  title,
  textSearch,
}) => {
  const handleOpenModal = () => {
    const onClose = () => {
      closeModal?.();
    };

    const closeModal = openModal({
      title,
      content: content({ onClose }),
      width: '50%',
      footer: null,
      centered: true,
    });
  };

  return (
    <div
      style={{
        display: 'flex',
      }}
    >
      <Input
        disabled
        value={label}
        style={{ width: 250, height: 30, marginRight: 10 }}
      />
      <div
        onClick={handleOpenModal}
        style={{
          alignItems: 'center',
          border: '1px solid var(--color-active)',
          color: 'var(--color-active)',
          height: '30px',
          padding: '3px 16px',
          display: 'inline-block',
          borderRadius: '100px',
          cursor: 'pointer',
        }}
      >
        <SearchOutlined style={{ marginRight: 5 }} />
        <Text variant="span" style={{ color: 'var(--color-active)' }}>
          {textSearch}
        </Text>
      </div>
    </div>
  );
};

export default SearchWithModal;
