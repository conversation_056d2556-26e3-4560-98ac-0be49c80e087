@import 'tailwindcss';
@import './variables.scss';
@import './common.scss';
@import './components.scss';

body {
  color: var(--foreground);
  /* background: var(--background); */
  font-family: Arial, Helvetica, sans-serif;
}

@font-face {
  font-family: 'NotoSansJP';
  src: url('/fonts/NotoSansJP.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

.ant-menu-submenu-popup .ant-menu-sub {
  background-color: #151519 !important;
}

.ant-menu-submenu-popup .ant-menu-item.ant-menu-item-only-child {
  background: rgba(255, 255, 255, 0.2) 0% 0% no-repeat padding-box !important;
}
