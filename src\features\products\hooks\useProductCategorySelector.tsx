import { useEffect, useState } from 'react';
import { ProductCategoryI } from '../types';

export const useProductCategorySelector = (
  initialValue?: ProductCategoryI,
  onChange?: (code: string) => void,
) => {
  const [selected, setSelected] = useState<ProductCategoryI | null>(
    initialValue ?? null,
  );

  useEffect(() => {
    if (initialValue) setSelected(initialValue);
  }, [initialValue]);

  const handleSelect = (value: ProductCategoryI, onClose?: () => void) => {
    setSelected(value);
    onChange?.(value.code);
    onClose?.();
  };

  return {
    selected,
    setSelected,
    handleSelect,
  };
};
