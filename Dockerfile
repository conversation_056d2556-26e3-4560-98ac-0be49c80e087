# ベースとなるイメージを指定
FROM node:22-alpine

# ワーキングディレクトリの指定
WORKDIR /app/

# タイムゾーンの設定
RUN apk --update add tzdata && \
    cp /usr/share/zoneinfo/Asia/Tokyo /etc/localtime && \
    apk del tzdata && \
    apk add --no-cache bash

# パッケージファイルをコピー
COPY ./package.json ./package-lock.json /app/

# 依存関係をインストール
RUN npm install

# ソースコードをコピー
COPY . .

# コンテナ内のポートを公開
EXPOSE 3000

# アプリケーションを起動
CMD ["npm", "run", "dev"]

