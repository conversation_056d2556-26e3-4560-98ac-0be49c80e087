export interface MasterCodeOptionI {
  label: string;
  value: any;
}

export interface GetMasterCodeParamsI {
  code_kbn: string[];
  page?: number;
  pageSize?: number;
  searchKeyword?: string;
}

export interface MasterCodeDataI {
  code: string;
  code_name: string;
}

export interface InformationUserCreateUpdateI {
  created_at: string;
  updated_at: string;
  created_by: {
    name: string;
  };
  updated_by: {
    name: string;
  };
}

export interface RowDataI {
  id: number;
  color?: string;
}

export interface RowComponentI<T = RowDataI> {
  getData: () => T;
  getTable: () => TableComponentI<T>;
  getElement: () => HTMLElement;
}

export interface TableComponentI<T = RowDataI> {
  getRows: () => RowComponentI<T>[];
}

export interface CellComponentI<T = RowDataI> {
  getRow: () => RowComponentI<T>;
  getValue: () => any;
  getElement: () => HTMLElement;
}
