'use client';

import { Button } from '@/components/atoms/Button';
import { InputSearch } from '@/components/atoms/Input';
import { Table } from '@/components/atoms/Table';
import usePagination from '@/hooks/usePagination';
import { openModal } from '@/utils/modal';
import { SearchOutlined } from '@ant-design/icons';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { useProductClassificationModal } from '../../hooks/useProductClassificationModal';
import { ProductClassificationDataI } from '../../types';

interface ProductClassificationModalPropsI {
  onSelect?: (
    selectedValues: string[],
    selectedItems?: ProductClassificationDataI[],
  ) => void;
  title?: string;
  initialSelectedValues?: ProductClassificationDataI[];
}

export const ProductClassificationModal = {
  open: ({
    onSelect,
    title,
    initialSelectedValues,
  }: ProductClassificationModalPropsI = {}) => {
    const ProductClassificationModalContent = ({
      onCloseModal,
    }: {
      onCloseModal: () => void;
    }) => {
      const { t } = useTranslation('products');
      const { pagination, setDefaultPage } = usePagination();

      const [keyword, setKeyword] = useState('');

      const {
        data,
        isFetching,
        columns,
        refetch,
        applyFilters,
        selectedItems,
      } = useProductClassificationModal(
        {
          page: pagination.current,
          pageSize: pagination.pageSize,
        },
        setDefaultPage,
        initialSelectedValues,
      );

      const handleSelect = () => {
        if (selectedItems.length > 0) {
          const selectedValues = selectedItems.map((item) => item.code);
          onSelect?.(selectedValues, selectedItems);
        }

        // Close the modal after selection
        onCloseModal();
      };

      return (
        <div style={{ maxWidth: '900px' }}>
          <div style={{ marginTop: 24, marginBottom: 32 }}>
            <InputSearch
              placeholder={t('productClassificationSearchPlaceholder')}
              allowClear
              enterButton={t('search')}
              value={keyword}
              prefix={<SearchOutlined />}
              onChange={(e) => {
                const value = e.target.value;
                setKeyword(value);
              }}
              onSearch={(value: string) => {
                applyFilters(value);
              }}
            />
          </div>

          <Table<ProductClassificationDataI>
            columns={columns}
            dataSource={data?.data}
            rowKey="id"
            pagination={{
              ...pagination,
              total: data?.meta?.total || 0,
            }}
            size="small"
            scroll={{ y: 300 }}
            loading={isFetching}
            onRefresh={refetch}
          />

          <div style={{ marginTop: 24, textAlign: 'left' }}>
            <Button
              type="primary"
              label={t('select')}
              onClick={handleSelect}
              autoInsertSpace={false}
            />
          </div>
        </div>
      );
    };

    const closeModal = openModal({
      title,
      content: (
        <ProductClassificationModalContent
          onCloseModal={() => closeModal?.()}
        />
      ),
      width: 'fit-content',
      footer: null,
      centered: true,
    });

    return closeModal;
  },
};
