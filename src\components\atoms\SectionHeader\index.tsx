import { Text } from '@/components/atoms/Text';

export interface SectionHeaderPropsI {
  title: string;
  className?: string;
}

export const SectionHeader = ({ title, className }: SectionHeaderPropsI) => {
  return (
    <div className={`flex items-center ${className}`}>
      <div className="h-6 w-1 rounded bg-teal-500" />
      <Text variant="h4" className="ml-2">
        {title}
      </Text>
    </div>
  );
};
