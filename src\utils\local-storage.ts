export class LocalStorageUtil {
  /**
   * Save a value to localStorage (automatically stringified).
   */
  static set<T>(key: string, value: T): void {
    try {
      const json = JSON.stringify(value);
      localStorage.setItem(key, json);
    } catch (err) {
      console.error(`Error saving key "${key}" to localStorage`, err);
    }
  }

  /**
   * Retrieve a value from localStorage and parse it.
   */
  static get<T>(key: string): T | null {
    try {
      const json = localStorage.getItem(key);
      if (json === null) return null;

      return JSON.parse(json) as T;
    } catch (err) {
      console.error(`Error reading key "${key}" from localStorage`, err);

      return null;
    }
  }

  /**
   * Remove a specific key from localStorage.
   */
  static remove(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (err) {
      console.error(`Error removing key "${key}" from localStorage`, err);
    }
  }

  /**
   * Clear all keys from localStorage.
   */
  static clear(): void {
    try {
      localStorage.clear();
    } catch (err) {
      console.error('Error clearing localStorage', err);
    }
  }

  /**
   * Check if a key exists in localStorage.
   */
  static has(key: string): boolean {
    return localStorage.getItem(key) !== null;
  }
}
