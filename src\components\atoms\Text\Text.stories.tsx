import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { Text, type TextVariantT } from './index';

const ThemedText = withThemeProvider(Text);

const meta: Meta<typeof ThemedText> = {
  title: 'Atoms/Text',
  component: ThemedText,
  tags: ['autodocs'],
  argTypes: {
    children: { control: 'text' },
    as: { control: 'text' },
    variant: {
      control: { type: 'select' },
      options: [
        'h1',
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
        'p',
        'body1',
        'body2',
        'span',
        'caption',
      ] as TextVariantT[],
    },
    className: { control: 'text' },
  },
  parameters: {
    docs: {
      description: {
        component:
          'Textコンポーネントは、アプリケーション内のすべてのテキストをレンダリングするために使用される汎用性の高いコンポーネントです。見出し、段落、キャプションなど、さまざまな種類のテキストをレンダリングするために使用できる一連のバリアントを提供します。また、セマンティックな目的で役立つ、テキストをレンダリングするために使用すべき基になるHTML要素を指定することもできます。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Text>;

export const Default: StoryT = {
  args: {
    children: 'This is a default text component.',
    variant: 'p',
  },
  parameters: {
    docs: {
      description: {
        story:
          '段落としてレンダリングされるデフォルトのテキストコンポーネント。',
      },
    },
  },
};

export const AllVariants: StoryT = {
  render: () => (
    <div>
      <Text variant="h1">Heading 1</Text>
      <Text variant="h2">Heading 2</Text>
      <Text variant="h3">Heading 3</Text>
      <Text variant="h4">Heading 4</Text>
      <Text variant="h5">Heading 5</Text>
      <Text variant="h6">Heading 6</Text>
      <Text variant="p">This is a paragraph (p variant).</Text>
      <Text variant="body1">This is body1 text.</Text>
      <Text variant="body2">This is body2 text.</Text>
      <Text variant="caption">This is a caption.</Text>
      <p>
        This is a sentence with{' '}
        <Text variant="span" className="font-bold text-blue-500">
          span
        </Text>{' '}
        inside.
      </p>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: '利用可能なすべてのテキストバリアントのデモンストレーション。',
      },
    },
  },
};

export const CustomTag: StoryT = {
  args: {
    children: 'This text is inside a div, with paragraph styles.',
    as: 'div',
    variant: 'p',
  },
  parameters: {
    docs: {
      description: {
        story:
          '`as`プロップを使用して、Textコンポーネントを異なるHTML要素としてレンダリングできます。',
      },
    },
  },
};
