import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { UserOutlined } from '@ant-design/icons';
import { Meta, StoryObj } from '@storybook/react-vite';
import { Avatar as AntdAvatar } from 'antd';
import { Avatar } from './index';

const ThemedAvatar = withThemeProvider(Avatar);

const meta: Meta<typeof Avatar> = {
  title: 'Atoms/Avatar',
  component: ThemedAvatar,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'アバターは、ユーザーまたはエンティティを表すために使用されます。画像、アイコン、またはフォールバックとして文字を表示できます。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Avatar>;

export const Default: StoryT = {
  args: {
    size: 64,
    icon: <UserOutlined />,
  },
  parameters: {
    docs: {
      description: {
        story:
          '画像が提供されていない場合にアイコンを表示できるデフォルトのアバター。',
      },
    },
  },
};

export const WithImage: StoryT = {
  args: {
    size: 64,
    src: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
  },
  parameters: {
    docs: {
      description: {
        story: 'アバターはユーザー画像を表示できます。',
      },
    },
  },
};

export const WithCharacters: StoryT = {
  args: {
    size: 64,
    children: 'USER',
  },
  parameters: {
    docs: {
      description: {
        story: '画像が利用できない場合、アバターはイニシャルを表示できます。',
      },
    },
  },
};

export const Square: StoryT = {
  args: {
    size: 64,
    shape: 'square',
    icon: <UserOutlined />,
  },
  parameters: {
    docs: {
      description: {
        story: 'アバターは正方形としてレンダリングできます。',
      },
    },
  },
};

export const Group: StoryT = {
  render: () => (
    <AntdAvatar.Group>
      <ThemedAvatar src="https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png" />
      <ThemedAvatar style={{ backgroundColor: '#f56a00' }}>K</ThemedAvatar>
      <ThemedAvatar
        style={{ backgroundColor: '#87d068' }}
        icon={<UserOutlined />}
      />
    </AntdAvatar.Group>
  ),
  parameters: {
    docs: {
      description: {
        story: '複数のアバターをグループ化できます。',
      },
    },
  },
};
