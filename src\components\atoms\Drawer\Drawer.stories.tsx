import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { Meta, StoryObj } from '@storybook/react-vite';
import React from 'react';
import { Button } from '../Button';
import { Drawer } from './index';

const ThemedDrawer = withThemeProvider(Drawer);
const ThemedButton = withThemeProvider(Button);

const meta: Meta<typeof Drawer> = {
  title: 'Atoms/Drawer',
  component: ThemedDrawer,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'ドロワーは、ナビゲーションメニュー、フォーム、設定などの補足コンテンツを表示するために画面の端からスライドして表示されるパネルです。メインビューを乱すことなく追加機能へのアクセスを提供するのに非常に便利です。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Drawer>;

export const Default: StoryT = {
  render: () => {
    const [open, setOpen] = React.useState(false);

    const showDrawer = () => {
      setOpen(true);
    };

    const onClose = () => {
      setOpen(false);
    };

    return (
      <>
        <ThemedButton type="primary" onClick={showDrawer} label="Open Drawer" />
        <ThemedDrawer
          title="Basic Drawer"
          placement="right"
          onClose={onClose}
          open={open}
        >
          <p>Some contents...</p>
          <p>Some contents...</p>
          <p>Some contents...</p>
        </ThemedDrawer>
      </>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'これはドロワーコンポーネントのデフォルトの状態です。',
      },
    },
  },
};
