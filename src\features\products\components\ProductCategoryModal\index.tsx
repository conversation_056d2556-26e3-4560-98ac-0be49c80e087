import { Button } from '@/components/atoms';
import { InputSearch } from '@/components/atoms/Input';
import { Table } from '@/components/atoms/Table';
import { MAX_INPUT_LENGTH } from '@/constants/common';
import usePagination from '@/hooks/usePagination';
import { SearchOutlined } from '@ant-design/icons';
import { Radio as AntdRadio } from 'antd';
import { useTranslation } from 'next-i18next';
import React, { useCallback, useState } from 'react';
import { useProductCategoryModal } from '../../hooks/useProductCategoryModal';
import { ProductCategoryDataI, ProductCategoryI } from '../../types';

interface ProductCategoryModalPropsI {
  onSelect?: (selected: ProductCategoryI) => void;
}

const ProductCategoryModal: React.FC<ProductCategoryModalPropsI> = ({
  onSelect,
}) => {
  const { t } = useTranslation('products');
  const [keyword, setKeyword] = useState('');
  const { pagination, setDefaultPage } = usePagination();
  const [selectedCode, setSelectedCode] = useState<string | null>('');

  const { data, isFetching, columns, refetch, applyFilters } =
    useProductCategoryModal(
      pagination.current,
      pagination.pageSize,
      setDefaultPage,
    );

  const handleSearch = useCallback(() => {
    applyFilters(keyword);
  }, [keyword, applyFilters]);

  const handleConfirmSelect = () => {
    const selectedItem = data?.result?.find(
      (item: ProductCategoryDataI) => item.code === selectedCode,
    );
    if (onSelect) {
      onSelect({
        code: selectedItem?.code,
        code_name: selectedItem?.code_name,
        code_kbn: selectedItem?.code_kbn,
      });
    }
  };

  return (
    <div>
      <div style={{ marginTop: 30, marginBottom: 32 }}>
        <InputSearch
          placeholder={t('enterValueAndCode')}
          allowClear
          enterButton={t('search')}
          value={keyword}
          prefix={<SearchOutlined />}
          maxLength={MAX_INPUT_LENGTH}
          onChange={(e) => setKeyword(e.target.value)}
          onSearch={handleSearch}
        />
      </div>

      <AntdRadio.Group
        className="w-full"
        value={selectedCode}
        onChange={(e) => setSelectedCode(e.target.value)}
      >
        <Table<ProductCategoryDataI>
          columns={columns}
          dataSource={data?.result}
          rowKey="id"
          pagination={{
            ...pagination,
            total: data?.meta?.total || 0,
          }}
          size="small"
          scroll={{ y: 300 }}
          loading={isFetching}
          onRefresh={refetch}
          loadingMinHeight="20px"
        />
      </AntdRadio.Group>

      <div style={{ marginTop: 24, textAlign: 'left' }}>
        <Button
          type="primary"
          label={t('choise')}
          onClick={handleConfirmSelect}
        />
      </div>
    </div>
  );
};

export default ProductCategoryModal;
