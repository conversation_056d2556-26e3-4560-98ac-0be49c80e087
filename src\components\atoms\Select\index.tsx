import { themeColors } from '@/styles/theme';
import { CaretDownOutlined } from '@ant-design/icons';
import { Select as AntdSelect, SelectProps as AntdSelectProps } from 'antd';
import { styled } from 'styled-components';

const StyledSelect = styled(AntdSelect)`
  .ant-select-selector {
    box-shadow: var(--inner-box-shadow) !important;
  }
  &.ant-select-multiple {
    .ant-select-selection-item {
      background: #eaf8f6;
      border: 1px solid var(--link-text-table);
      border-radius: 6px;
      color: var(--link-text-table);
    }
    .ant-select-selection-item-remove {
      color: var(--link-text-table);
    }
  }
` as typeof AntdSelect;

export const Select = (props: AntdSelectProps) => {
  return (
    <StyledSelect
      className="w-full"
      suffixIcon={
        <CaretDownOutlined
          style={{ pointerEvents: 'none', color: themeColors.iconColor }}
        />
      }
      {...props}
    />
  );
};
