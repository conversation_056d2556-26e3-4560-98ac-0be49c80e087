#!/bin/bash

source "$(dirname "$0")/utils.sh"

readonly UNCOMMITTED_CHANGES_MSG="Uncommitted changes detected. Please commit these changes before pushing."
readonly STAGED_CHANGES_MSG="Staged changes found. Please commit them before pushing."
readonly BUILD_FAILED_MSG="Build failed"
readonly BUILD_STORYBOOK_FAILED_MSG="Build storybook failed"

check_dependencies() {
    local dependencies=("git" "yarn")
    for cmd in "${dependencies[@]}"; do
        if ! command -v "$cmd" &>/dev/null; then
            print_error "❌ Error: Required command '$cmd' not found"
        fi
    done
}

check_uncommitted_changes() {
    if ! git diff-index --quiet HEAD --; then
        print_error "❌ Error: $UNCOMMITTED_CHANGES_MSG"
    fi
}

check_staged_changes() {
    if ! git diff --cached --quiet; then
        print_error "❌ Error: $STAGED_CHANGES_MSG"
    fi
}

build() {
    local commands=(
        "yarn build:$BUILD_FAILED_MSG"
    )

    for cmd_msg in "${commands[@]}"; do
        IFS=':' read -r cmd msg <<<"$cmd_msg"
        if ! $cmd; then
            print_error "❌ Error: $msg"
        fi
    done
}

push() {
    check_dependencies
    print_success "🔄 Starting pre-push checks"
    check_uncommitted_changes
    check_staged_changes
    build
    print_success "✅ All pre-push checks passed"
}

push
