import { Checkbox } from '@/components/atoms';
import { CellComponentI, RowDataI } from '@/types/common';
import { TFunction } from 'i18next';
import ReactDOM from 'react-dom/client';
import { ColumnDefinition } from 'react-tabulator';
import { ProductOrderFormFieldKeyE } from '../enums';

export const getSupplierFactoryColumns = (
  t: TFunction<readonly ['product-order', 'common'], undefined>,
  handleCheckboxChange: (id: number, field: string, checked: boolean) => void,
): ColumnDefinition[] => [
  {
    title: 'No',
    field: ProductOrderFormFieldKeyE.ID,
    width: 50,
    headerHozAlign: 'center',
    hozAlign: 'center',
  },
  {
    title: t('common:delete'),
    width: 100,
    headerHozAlign: 'center',
    hozAlign: 'center',
    field: ProductOrderFormFieldKeyE.DELETE,
    formatter: (cell: CellComponentI<RowDataI>) => {
      const record = cell.getRow().getData();
      const el = document.createElement('div');
      const value = cell.getValue();
      const root = ReactDOM.createRoot(el);
      root.render(
        <Checkbox
          defaultChecked={value}
          onChange={(e) =>
            handleCheckboxChange(record.id, 'delete', e.target.checked)
          }
        />,
      );

      return el;
    },
    cellClick: (e: Event, cell: CellComponentI<RowDataI>) => {
      const target = e.target as HTMLElement;
      if (target?.classList?.contains('receipt-checkbox')) {
        const record = cell.getRow().getData();
        const checkbox = target as HTMLInputElement;
        handleCheckboxChange(record.id, 'delete', checkbox.checked);
      }
    },
  },
  {
    title: t('supplierFactory'),
    field: ProductOrderFormFieldKeyE.PMS_SUPPLIER_NAME,
  },
];

export const getPurchasePriceColumns = (
  t: TFunction<readonly ['product-order', 'common'], undefined>,
  handleValidatePrice: (cell: CellComponentI<RowDataI>, value: any) => boolean,
): ColumnDefinition[] => [
  {
    title: 'No',
    field: ProductOrderFormFieldKeyE.NO,
    width: 50,
    headerHozAlign: 'center',
    hozAlign: 'center',
  },
  {
    title: t('color'),
    field: ProductOrderFormFieldKeyE.COLOR,
    headerHozAlign: 'center',
    formatter: function (cell: CellComponentI<RowDataI>) {
      const row = cell.getRow();
      const table = row.getTable();
      const rows = table.getRows();
      const index = rows.indexOf(row);
      const currentColor = cell.getValue();
      const el = cell.getElement();
      let isFirstInGroup = true;
      let isLastInGroup = true;
      if (index > 0) {
        const prevColor = rows[index - 1].getData().color;
        if (prevColor === currentColor) {
          isFirstInGroup = false;
        }
      }

      if (index < rows.length - 1) {
        const nextColor = rows[index + 1].getData().color;
        if (nextColor === currentColor) {
          isLastInGroup = false;
        }
      }

      if (!isLastInGroup) {
        el.style.setProperty('border-bottom', 'none', 'important');
        row
          .getElement()
          .style.setProperty('border-bottom', 'none', 'important');
      }

      if (!isFirstInGroup) {
        return '';
      }

      return currentColor;
    },
  },
  {
    title: t('size'),
    field: ProductOrderFormFieldKeyE.SIZE,
    headerHozAlign: 'center',
  },
  {
    title: t('priceTaxEx'),
    field: ProductOrderFormFieldKeyE.PRICE_TAX_EX,
    editor: 'input',
    headerHozAlign: 'center',
    hozAlign: 'right',
    validator: { type: handleValidatePrice },
  },
  {
    title: t('priceTaxIn'),
    field: ProductOrderFormFieldKeyE.PRICE_TAX_IN,
    editor: 'input',
    headerHozAlign: 'center',
    hozAlign: 'right',
    validator: { type: handleValidatePrice },
  },
  {
    title: 'FOB(JPY)',
    field: ProductOrderFormFieldKeyE.FOB,
    editor: 'input',
    headerHozAlign: 'center',
    hozAlign: 'right',
    validator: { type: handleValidatePrice },
  },
];

export const getContractColumns = (
  t: TFunction<readonly ['product-order', 'common'], undefined>,
  handleOpenContractModal: (type: 'create' | 'edit', index: number) => void,
): ColumnDefinition[] => [
  {
    title: 'No',
    field: ProductOrderFormFieldKeyE.NO,
    width: 50,
    headerHozAlign: 'center',
    hozAlign: 'center',
  },
  {
    title: t('companyName'),
    field: ProductOrderFormFieldKeyE.COMPANY_NAME,
    headerHozAlign: 'center',
    formatter: function (cell: CellComponentI<RowDataI>) {
      const currentColor = cell.getValue();
      const el = document.createElement('div');
      const root = ReactDOM.createRoot(el);
      const row = cell.getRow();
      const table = row.getTable();
      const rows = table.getRows();
      const index = rows.indexOf(row);
      root.render(
        <a
          href="#"
          onClick={() => handleOpenContractModal('edit', index)}
          style={{ textDecoration: 'underline' }}
        >
          {currentColor}
        </a>,
      );

      return el;
    },
  },
  {
    title: t('contractDate'),
    field: ProductOrderFormFieldKeyE.CONTRACT_DATE,
    headerHozAlign: 'center',
    hozAlign: 'center',
  },
];
