import React from 'react';

interface EmptyTablePropsI {
  message: string;
  minHeight?: string | number;
}

export const EmptyTable: React.FC<EmptyTablePropsI> = ({
  message,
  minHeight = '300px',
}) => {
  return (
    <div
      style={{
        minHeight,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
        }}
      >
        <div
          style={{
            width: 20,
            height: 20,
            borderRadius: '50%',
            background: '#41414B',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#fff',
            fontWeight: 600,
            fontSize: 14,
          }}
        >
          !
        </div>
        <span style={{ color: '#41414B', fontSize: '14px' }}>{message}</span>
      </div>
    </div>
  );
};
