import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { Meta, StoryObj } from '@storybook/react-vite';
import { Checkbox as AntdCheckbox } from 'antd';
import { Checkbox } from './index';

const ThemedCheckbox = withThemeProvider(Checkbox);

const meta: Meta<typeof Checkbox> = {
  title: 'Atoms/Checkbox',
  component: ThemedCheckbox,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'チェックボックスを使用すると、ユーザーは選択肢のリストから1つ以上のオプションを選択できます。ラジオボタンとは異なり、ユーザーはグループ内の任意の数のチェックボックスを選択でき、何も選択しないこともすべて選択することも可能です。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Checkbox>;

export const Default: StoryT = {
  args: {
    label: 'Checkbox',
  },
  parameters: {
    docs: {
      description: {
        story: 'これはチェックボックスのデフォルトの状態です。',
      },
    },
  },
};

export const Disabled: StoryT = {
  args: {
    ...Default.args,
    disabled: true,
  },
  parameters: {
    docs: {
      description: {
        story: '操作できない無効なチェックボックス。',
      },
    },
  },
};

export const Checked: StoryT = {
  args: {
    ...Default.args,
    checked: true,
  },
  parameters: {
    docs: {
      description: {
        story: '事前に選択またはチェックされたチェックボックス。',
      },
    },
  },
};

export const Indeterminate: StoryT = {
  args: {
    ...Default.args,
    indeterminate: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          'チェックボックスのグループで「部分的にチェックされた」状態を表すためによく使用される、不確定な状態のチェックボックス。',
      },
    },
  },
};

export const CheckboxGroup: StoryT = {
  render: () => (
    <AntdCheckbox.Group>
      <ThemedCheckbox label="A" value="A" />
      <ThemedCheckbox label="B" value="B" />
      <ThemedCheckbox label="C" value="C" />
      <ThemedCheckbox label="D" value="D" />
    </AntdCheckbox.Group>
  ),
  parameters: {
    docs: {
      description: {
        story: '複数の選択を許可するチェックボックスのグループ。',
      },
    },
  },
};

export const DisabledCheckboxInGroup: StoryT = {
  render: () => (
    <AntdCheckbox.Group defaultValue={['A']}>
      <ThemedCheckbox label="A" value="A" />
      <ThemedCheckbox label="B" value="B" />
      <ThemedCheckbox label="C" value="C" disabled />
      <ThemedCheckbox label="D" value="D" />
    </AntdCheckbox.Group>
  ),
  parameters: {
    docs: {
      description: {
        story: '選択できない無効なオプションを含むチェックボックスグループ。',
      },
    },
  },
};
