services:
  next:
    # ビルドファイルの指定
    build:
      context: .
      dockerfile: Dockerfile

    # ホストOSのポートとコンテナのポートを指定
    ports:
      - 3001:3000

    # ネットワーク設定
    networks:
      - sail

    # ホストOSのフォルダとコンテナのフォルダを指定
    volumes:
      # ホストPCとコンテナのフォルダをつなぐための設定です。
      # delegatedの指定は「コンテナ上の更新がホスト上に反映するまで、遅延が発生するのを許容する」
      # 設定でパフォーマンスを上げるため設定しています。
      - ./:/app:delegated
      - ./node_modules:/app/node_modules
networks:
  sail:
    external: true
    name: sail
