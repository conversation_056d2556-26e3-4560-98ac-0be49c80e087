import { ProductDetailI } from '@/features/products/types';
import type { NextApiRequest, NextApiResponse } from 'next';

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ data: ProductDetailI }>,
) {
  const dummyProduct: ProductDetailI = {
    id: 1,
    pms_product_seq_no: 1,
    pms_product_no: '36-250134302-10',
    corporate_id: '1',
    pms_product_name: 'デモ商品',

    gender_category_code: 'MEN',
    gender_category: {
      code_kbn: 'GENDERCATEGORYCD',
      code: 'MEN',
      code_name: 'メンズ',
    },

    product_category_code: 'CODE082',
    product_category: {
      code_kbn: 'TMPCATECN',
      code: 'CODE082',
      code_name: 'test',
    },

    m_store_budget_type_fkid: 1,
    store_budget_type: {
      id: 1,
      gender_category_code: 'MEN',
      store_budget_type_code: 'HIGH',
      store_budget_type_name: 'ハイエンド',
    },

    pms_sales_plan_start_datetime: '2025-06-20T22:28:05.000000Z',
    pms_sales_plan_end_datetime: '2025-06-25T22:28:05.000000Z',
    pms_store_withdraw_datetime: '2025-06-24T22:28:05.000000Z',

    pms_sales_season_code: 'SP25',
    pms_sample_no: 'SP25-001',
    pms_planner_code: 'CODE152',
    pms_hs_code: '6403.20',
    pms_remarks: 'デモ用の商品データです',
    t_product_types: [
      {
        id: 1,
        t_pms_product_fkid: 1,
        m_pms_product_type_fkid: 1,
        product_type_fkid: {
          id: 1,
          pms_product_type_name: 'coca',
          corporate_id: 1,
          m_pms_product_type_group_fkid: 1,
          product_type_group: {
            id: 1,
            pms_product_type_group_name: 'ブランド',
            corporate_id: 1,
            sort_key: 1,
            hidden_flg: 0,
          },
          sort_key: 1,
          hidden_flg: 0,
        },
      },
      {
        id: 2,
        t_pms_product_fkid: 1,
        m_pms_product_type_fkid: 1,
        product_type_fkid: {
          id: 1,
          pms_product_type_name: 'coca',
          corporate_id: 1,
          m_pms_product_type_group_fkid: 1,
          product_type_group: {
            id: 1,
            pms_product_type_group_name: 'ブランド2',
            corporate_id: 1,
            sort_key: 1,
            hidden_flg: 0,
          },
          sort_key: 1,
          hidden_flg: 0,
        },
      },
    ],
    created_at: '2025-06-21T22:28:05.000000Z',
    updated_at: '2025-06-26T22:28:05.000000Z',
    created_by: { id: 1, name: 'Admin' },
    updated_by: { id: 2, name: 'Editor' },
  };

  res.status(200).json({ data: dummyProduct });
}
