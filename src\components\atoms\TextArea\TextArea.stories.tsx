import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { Meta, StoryObj } from '@storybook/react-vite';
import React from 'react';
import { TextArea } from './index';

const ThemedTextArea = withThemeProvider(TextArea);

const meta: Meta<typeof TextArea> = {
  title: 'Atoms/TextArea',
  component: ThemedTextArea,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'Ant DesignのTextAreaコンポーネントをラップし、カスタムスタイルを適用するカスタムTextAreaコンポーネント。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof TextArea>;

export const Default: StoryT = {
  render: () => {
    const [value, setValue] = React.useState('');

    return (
      <ThemedTextArea
        value={value}
        onChange={(e) => setValue(e.target.value)}
        placeholder="Enter text here..."
      />
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'TextAreaコンポーネントのデフォルトの状態です。',
      },
    },
  },
};

export const WithCharacterCount: StoryT = {
  render: () => {
    const [value, setValue] = React.useState('');

    return (
      <ThemedTextArea
        showCount
        maxLength={100}
        value={value}
        onChange={(e) => setValue(e.target.value)}
        placeholder="Enter text here..."
      />
    );
  },
  parameters: {
    docs: {
      description: {
        story: '文字カウンターと最大100文字の長さを持つテキストエリア。',
      },
    },
  },
};

export const AutoSize: StoryT = {
  render: () => {
    const [value, setValue] = React.useState('');

    return (
      <ThemedTextArea
        autoSize
        value={value}
        onChange={(e) => setValue(e.target.value)}
        placeholder="This text area will auto-resize based on content."
        style={{ resize: 'both' }}
      />
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          'コンテンツに合わせて自動的にサイズが変更されるTextArea。このストーリーではサイズ変更が再有効化されています。',
      },
    },
  },
};
