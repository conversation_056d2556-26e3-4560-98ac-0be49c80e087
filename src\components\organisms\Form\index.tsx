import {
  Button,
  Checkbox,
  DatePicker,
  EditableTable,
  Input,
  InputPassword,
  Radio,
  TableOptionsI,
  TextArea,
} from '@/components/atoms';
import { RequiredTag } from '@/components/atoms/RequiredTag';
import {
  MasterDataDropdown,
  SelectPropsI,
} from '@/components/organisms/MasterDataDropdown';
import { UploadOutlined } from '@ant-design/icons';
import { Form as AntdForm, Col, FormProps, Row } from 'antd';
import { Rule } from 'antd/es/form';
import { Dayjs } from 'dayjs';
import { useTranslation } from 'next-i18next';

import {
  preventInvalidInput,
  preventInvalidPaste,
} from '@/utils/input-validators';
import { ColumnDefinition } from 'react-tabulator';
import {
  StyledFieldInput,
  StyledFieldLabel,
  StyledFormContainer,
  StyledFormField,
} from './styled';
interface FormFieldOptionI {
  value: string | number;
  label: string;
}

export interface FormFieldConfigI {
  name: string;
  label?: string;
  labelButton?: string;
  type:
    | 'input'
    | 'input-search'
    | 'password'
    | 'textarea'
    | 'datepicker'
    | 'select'
    | 'checkbox'
    | 'radiogroup'
    | 'datepicker-range'
    | 'input-search'
    | 'checkboxgroup'
    | 'empty'
    | 'editable-table'
    | 'field-group'
    | 'button'
    | 'upload'
    | 'other';
  options?: FormFieldOptionI[];
  labelColSpan?: number;
  labelPosition?: 'left' | 'right' | 'center';
  rules?: Rule[];
  placeholder?: string | [string, string];
  disabled?: boolean;
  rows?: number;
  initialValue?:
    | string
    | number
    | boolean
    | null
    | Dayjs
    | [Dayjs | null, Dayjs | null]
    | Array<string | number>;
  editableTableConfig?: {
    data: any[];
    columns: ColumnDefinition[];
    tableOptions?: TableOptionsI;
    setData: (data: any[]) => void;
  };
  fields?: FormFieldConfigI[];
  style?: React.CSSProperties;
  picker?: 'date' | 'week' | 'month' | 'quarter' | 'year';
  format?: string;
  extra?: string | React.ReactNode;
  apiConfig?: SelectPropsI['apiConfig'];
  maxLength?: number;
  requiredText?: string;
  mode?: 'multiple' | 'tags';
  orderPosition?: number;
  regexPattern?: RegExp;
  render?: () => React.ReactNode;
  onClick?: () => void;
  isFullRow?: boolean;
  iconButton?: React.ReactNode;
  readOnly?: boolean;
  showTime?: boolean;
}

export interface FormPropsI extends FormProps {
  formFields: FormFieldConfigI[];
  numOfColumns?: number;
}

const FormField = ({
  label,
  name,
  type,
  options,
  labelColSpan,
  labelPosition = 'right',
  rules,
  placeholder,
  disabled,
  rows = 1,
  editableTableConfig,
  fields,
  labelButton,
  style = {},
  apiConfig,
  requiredText,
  mode,
  onClick,
  iconButton,
  showTime,
  regexPattern,
  ...rest
}: FormFieldConfigI) => {
  const { t } = useTranslation('components');
  const renderField = () => {
    if (type === 'field-group' && Array.isArray(fields)) {
      return (
        <div style={{ display: 'flex', gap: 8 }}>
          {fields.map((f: FormFieldConfigI) => (
            <div
              key={f.name}
              style={{ flex: f.type === 'button' ? 'none' : 1, minWidth: 0 }}
            >
              <FormField
                {...f}
                labelColSpan={0}
                labelPosition={labelPosition}
                style={{ padding: 0 }}
              />
            </div>
          ))}
        </div>
      );
    }

    switch (type) {
      case 'textarea':
        return <TextArea rows={rows} className="w-full" />;
      case 'datepicker':
        return (
          <DatePicker
            className="w-full"
            showTime={showTime}
            placeholder={
              typeof placeholder === 'string' ? placeholder : undefined
            }
            {...rest}
          />
        );
      case 'select':
        return (
          <MasterDataDropdown
            apiConfig={apiConfig}
            options={options}
            mode={mode}
          />
        );
      case 'checkbox':
        return <Checkbox label="" />;
      case 'radiogroup':
        return <Radio type="group" options={options} />;
      case 'upload':
        return <Button icon={<UploadOutlined />} label={t('form.upload')} />;
      case 'checkboxgroup':
        return <Checkbox.Group options={options} />;
      case 'password':
        return <InputPassword />;
      case 'datepicker-range':
      case 'other':
      case 'input-search':
        return rest.render ? rest.render() : null;
      case 'empty':
        return null;
      case 'editable-table':
        return editableTableConfig ? (
          <EditableTable pagination={false} {...editableTableConfig} />
        ) : null;
      case 'input':
        return (
          <Input
            disabled={disabled}
            maxLength={rest?.maxLength}
            readOnly={rest?.readOnly}
            onBeforeInput={(e) => preventInvalidInput(e, regexPattern)}
            onPaste={(e) => preventInvalidPaste(e, regexPattern)}
          />
        );
      case 'button':
        return (
          <Button
            label={labelButton ?? ''}
            className="float-right"
            onClick={onClick}
            icon={iconButton}
          />
        );
    }
  };

  return (
    <StyledFormField>
      <StyledFieldLabel
        hidden={!label}
        $labelColSpan={labelColSpan}
        $labelPosition={labelPosition}
      >
        {requiredText && <RequiredTag text={requiredText} />}
        {label}
      </StyledFieldLabel>
      <StyledFieldInput style={style}>
        <AntdForm.Item
          name={name}
          rules={rules}
          valuePropName={type === 'checkbox' ? 'checked' : 'value'}
          {...rest}
        >
          {renderField()}
        </AntdForm.Item>
      </StyledFieldInput>
    </StyledFormField>
  );
};

export const Form = ({ formFields, numOfColumns = 1, ...rest }: FormPropsI) => {
  const renderFormFields = () => {
    const renderRow = (fields: FormFieldConfigI[], key: string) => (
      <Row key={key}>
        {fields.map((field) => (
          <Col span={24 / numOfColumns} key={field.name}>
            <FormField {...field} />
          </Col>
        ))}
        {fields.length < numOfColumns && (
          <Col span={24 - fields.length * (24 / numOfColumns)}>
            <StyledFormField>
              <StyledFieldInput style={{ width: '100%' }} />
            </StyledFormField>
          </Col>
        )}
      </Row>
    );

    const rows: JSX.Element[] = [];
    let buffer: FormFieldConfigI[] = [];

    formFields.forEach((field, index) => {
      if (field.isFullRow) {
        if (buffer.length > 0) {
          rows.push(renderRow(buffer, `row-buffer-${index}`));
          buffer = [];
        }
        rows.push(
          <Row key={`row-isFullRow-${field.name}`}>
            <Col span={24}>
              <FormField {...field} />
            </Col>
          </Row>,
        );
      } else {
        buffer.push(field);
        if (buffer.length === numOfColumns) {
          rows.push(renderRow(buffer, `row-buffer-${index}`));
          buffer = [];
        }
      }
    });

    if (buffer.length > 0) {
      rows.push(renderRow(buffer, 'row-buffer-last'));
    }

    return rows;
  };

  return (
    <StyledFormContainer>
      <AntdForm {...rest}>{renderFormFields()}</AntdForm>
    </StyledFormContainer>
  );
};
