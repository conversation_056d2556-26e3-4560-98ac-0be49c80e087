import { useEffect, useRef } from 'react';

type UseMouseOverOutOptionsT = {
  onMouseOverOutside?: () => void;
  onMouseOverInside?: () => void;
};

export function useMouseOverOut<T extends HTMLElement>(
  options?: UseMouseOverOutOptionsT,
) {
  const ref = useRef<T | null>(null);

  function handleMouseMove(e: MouseEvent) {
    if (!ref.current) {
      return;
    }

    const bounds = ref.current.getBoundingClientRect();
    const inside =
      e.clientX >= bounds.left &&
      e.clientX <= bounds.right &&
      e.clientY >= bounds.top &&
      e.clientY <= bounds.bottom;

    if (inside) {
      options?.onMouseOverInside?.();
    } else {
      options?.onMouseOverOutside?.();
    }
  }

  useEffect(() => {
    document.addEventListener('mousemove', handleMouseMove);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
    };
  }, [options]);

  return { ref };
}
