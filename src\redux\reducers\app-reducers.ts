import { createSlice } from '@reduxjs/toolkit';

interface AppStateI {
  isLoading: boolean;
  isOpenSidebar: boolean;
}

const initialState = { isLoading: false, isOpenSidebar: false } as AppStateI;

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    showLoading(state) {
      state.isLoading = true;
    },
    hideLoading(state) {
      state.isLoading = false;
    },
    toggleSidebar(state) {
      state.isOpenSidebar = !state.isOpenSidebar;
    },
  },
});

export const { showLoading, hideLoading, toggleSidebar } = appSlice.actions;

export const appReducer = appSlice.reducer;
