import { apiRequest } from '@/api/api-request';
import { ApiEndpointE, PaginationParamsE } from '@/enums';
import {
  MasterDataI,
  SampleDetailI,
  SampleFlagUpdateI,
  SampleI,
  SampleSearchFormValuesI,
} from '@/features/sample-information/types';
import { ApiResponseDetailI, ApiResponseListI } from '@/types/api';
import { DateHelper } from '@/utils';
import { SampleFormFieldKeyE } from './enums';

const buildSampleQueryParams = (
  page: number,
  perPage: number,
  filters: SampleSearchFormValuesI,
): URLSearchParams => {
  const params = new URLSearchParams();

  const multiValueFields = [
    SampleFormFieldKeyE.ORDER,
    SampleFormFieldKeyE.PAYMENT,
    SampleFormFieldKeyE.RECEIPT,
  ];

  for (const [key, value] of Object.entries(filters)) {
    if (value === undefined || value === null || value === '') {
      continue;
    }

    const isMultiValue =
      multiValueFields.includes(key as SampleFormFieldKeyE) &&
      Array.isArray(value);

    if (isMultiValue) {
      for (const v of value) {
        params.append(`${key}[]`, String(v));
      }
      continue;
    }

    if (
      key === SampleFormFieldKeyE.REQUEST_DATE_FROM ||
      key === SampleFormFieldKeyE.REQUEST_DATE_TO
    ) {
      const formatted = DateHelper.formatDate({
        date: value,
        format: DateHelper.FORMAT.FORMAT_31,
      });

      if (formatted) {
        params.append(key, formatted);
      }
      continue;
    }

    if (key === SampleFormFieldKeyE.PLANNING_DATE) {
      const formatted = DateHelper.formatDate({
        date: value,
        format: DateHelper.FORMAT.FORMAT_32,
      });

      if (formatted) {
        params.append(key, formatted);
      }
      continue;
    }

    params.append(key, String(value.trim()));
  }

  params.append(PaginationParamsE.PAGE, String(page));
  params.append(PaginationParamsE.PER_PAGE, String(perPage));

  return params;
};

export const getSamples = async (
  page: number,
  perPage: number,
  filters: SampleSearchFormValuesI,
): Promise<ApiResponseListI<SampleI>['result']> => {
  const params = buildSampleQueryParams(page, perPage, filters);
  const response = await apiRequest.get(ApiEndpointE.SAMPLES, {
    params,
  });

  return response.data.result;
};

export const updateSampleFlags = (payload: {
  flags: Array<SampleFlagUpdateI>;
}) => {
  return apiRequest.post(ApiEndpointE.SAMPLE_UPDATE_FLAGS, payload);
};

export const getSample = async (
  id: number,
): Promise<ApiResponseDetailI<SampleDetailI>> => {
  const response = await apiRequest.get(`${ApiEndpointE.SAMPLES}/${id}`);

  return response.data;
};

export const createSample = (payload: any) => {
  return apiRequest.post(ApiEndpointE.SAMPLES, payload);
};

export const getSupplier = async (params: { hidden_flg: string }) => {
  const response = await apiRequest.get(ApiEndpointE.SUPPLIER, { params });

  return response.data;
};

export const getSelectOptions = async (
  endPoint: string,
  params: any,
): Promise<ApiResponseListI<MasterDataI>> => {
  const response = await apiRequest.get(endPoint, { params });

  return response.data;
};
