import { useState } from 'react';
import { ProductOrderDetailI } from '../types';

export function useSupplierFactory() {
  const [supplierFactories, setSupplierFactories] = useState<
    ProductOrderDetailI['supplier_factories']
  >([]);

  const handleSubmitSupplierFactoryModal = (data: any[]) => {
    setSupplierFactories(
      (prevData: ProductOrderDetailI['supplier_factories']) => {
        const newData = data
          .filter((item) => item.delete === false)
          .map((item) => ({
            id: item.id,
            delete: item.delete,
            pms_supplier_factory_code: item.pms_supplier_factory_code,
            pms_supplier_name: item.pms_supplier_name,
          }))
          .filter((newItem) => {
            return !prevData?.some(
              (existingItem: any) => existingItem.id === newItem.id,
            );
          });

        return [...(prevData || []), ...newData];
      },
    );
  };

  const handleCheckboxChange = (
    id: number,
    field: string,
    checked: boolean,
  ) => {
    setSupplierFactories(
      (prevData: ProductOrderDetailI['supplier_factories']) =>
        prevData?.map((item: any) =>
          item.id === id ? { ...item, [field]: checked } : item,
        ),
    );
  };

  return {
    supplierFactories,
    setSupplierFactories,
    handleSubmitSupplierFactoryModal,
    handleCheckboxChange,
  };
}
