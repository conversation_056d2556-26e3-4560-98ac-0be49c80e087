export interface ProductSearchFormValuesI {
  search?: string;
  product_no?: string;
  product_name?: string;
  planner_code?: string[];
  gender_category_code?: string[];
  product_category_code?: string[];
  supplier_code?: string;
  local_shipping_plan_datetime_from?: string;
  local_shipping_plan_datetime_to?: string;
  local_shipping_result_datetime_from?: string;
  local_shipping_result_datetime_to?: string;
  product_remarks?: string;
  product_categories?: string[];
  fabric_seq_no?: string;
  fabric_name?: string;
  colors?: string[];
  sizes?: string[];
  order_type?: string[];
  store_arrival_plan_datetime_from?: string;
  store_arrival_plan_datetime_to?: string;
  store_arrival_result_datetime_from?: string;
  store_arrival_result_datetime_to?: string;
}

export interface ProductI {
  id: number;
  product_no: string;
  product_name: string;
  sample_no: string;
  planner_code: string;
  gender_category_code: string;
  product_category_code: string;
  product_remarks: string | null;
  product_media: ProductImageI;
}

export interface MediaFileI {
  media_file_seq_no: number;
  media_file_kbn: string;
  media_file_logical_name: string;
  media_file_physical_name: string;
  media_file_path: string;
  sort_key: number;
}

export interface ProductImageI {
  id: number;
  product_fkid: string;
  product_media_file_kbn: string;
  media_file_fkid: string;
  media_file: MediaFileI;
}

export interface MasterDataI {
  id: number;
  label: string;
}

export interface ProductClassificationDataI {
  id: number;
  code: string;
  classification: string;
  group: string;
}
