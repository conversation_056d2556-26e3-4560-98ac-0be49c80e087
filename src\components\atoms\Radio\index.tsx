import {
  Radio as AntdRadio,
  RadioProps as AntdRadioProps,
  RadioGroupProps,
} from 'antd';

type BasePropsT = {
  label?: string;
};

type RadioButtonPropsT = AntdRadioProps & { type?: 'default' };
type RadioGroupPropsWithTypeT = RadioGroupProps & {
  type: 'group' | 'tabs';
  direction?: 'horizontal' | 'vertical';
};

export type RadioPropsT = BasePropsT &
  (RadioButtonPropsT | RadioGroupPropsWithTypeT);

export const Radio = ({ ...props }: RadioPropsT) => {
  const { type = 'default', label } = props;

  if (type === 'tabs') {
    const { options, ...rest } = props as RadioGroupPropsWithTypeT;

    return (
      <AntdRadio.Group buttonStyle="solid" {...rest}>
        {options?.map((option) => {
          if (typeof option === 'object' && option !== null) {
            return (
              <AntdRadio.Button
                key={String(option.value)}
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </AntdRadio.Button>
            );
          }

          return (
            <AntdRadio.Button key={String(option)} value={option}>
              {String(option)}
            </AntdRadio.Button>
          );
        })}
      </AntdRadio.Group>
    );
  }

  if (type === 'group') {
    const { options, direction, ...rest } = props as RadioGroupPropsWithTypeT;
    const radioStyle = direction === 'vertical' ? { display: 'block' } : {};

    return (
      <AntdRadio.Group {...rest}>
        {options?.map((option) => {
          if (typeof option === 'object' && option !== null) {
            return (
              <AntdRadio
                style={radioStyle}
                key={String(option.value)}
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </AntdRadio>
            );
          }

          return (
            <AntdRadio style={radioStyle} key={String(option)} value={option}>
              {String(option)}
            </AntdRadio>
          );
        })}
      </AntdRadio.Group>
    );
  }

  return (
    <AntdRadio {...props} className="align-middle">
      {label}
    </AntdRadio>
  );
};
