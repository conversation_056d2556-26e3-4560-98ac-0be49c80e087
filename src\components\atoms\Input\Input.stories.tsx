import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { UserOutlined } from '@ant-design/icons';
import { Meta, StoryObj } from '@storybook/react-vite';
import { Input as AntdInput } from 'antd';
import { Input, InputSearch } from './index';

const ThemedInput = withThemeProvider(Input);
const ThemedSearch = withThemeProvider(InputSearch);
const ThemedTextArea = withThemeProvider(AntdInput.TextArea);

const meta: Meta<typeof Input> = {
  title: 'Atoms/Input',
  component: ThemedInput,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          '入力は、ユーザーがテキストやデータを入力および編集できるフォーム要素です。名前、メールアドレス、パスワード、数値など、さまざまな情報を取得するために使用できます。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Input>;

export const Default: StoryT = {
  args: {
    placeholder: 'Input',
  },
  parameters: {
    docs: {
      description: {
        story: '入力コンポーネントのデフォルトの状態です。',
      },
    },
  },
};

export const Disabled: StoryT = {
  args: {
    ...Default.args,
    disabled: true,
  },
  parameters: {
    docs: {
      description: {
        story: '操作できない無効な入力です。',
      },
    },
  },
};

export const WithPrefixIcon: StoryT = {
  args: {
    ...Default.args,
    prefix: <UserOutlined />,
  },
  parameters: {
    docs: {
      description: {
        story: 'テキストの前にアイコンが付いた入力。',
      },
    },
  },
};

export const WithSuffixIcon: StoryT = {
  args: {
    ...Default.args,
    suffix: <UserOutlined />,
  },
  parameters: {
    docs: {
      description: {
        story: 'テキストの後にアイコンが付いた入力。',
      },
    },
  },
};

export const TextArea: StoryObj<typeof AntdInput.TextArea> = {
  render: (args) => <ThemedTextArea {...args} />,
  args: {
    placeholder: 'Text Area',
  },
  parameters: {
    docs: {
      description: {
        story: '複数行のテキスト入力エリア。',
      },
    },
  },
};

export const InputNumber: StoryT = {
  args: {
    ...Default.args,
    type: 'number',
    placeholder: 'Number Input',
  },
  parameters: {
    docs: {
      description: {
        story: '数値のみを受け入れる入力。',
      },
    },
  },
};

export const ReadOnly: StoryT = {
  args: {
    ...Default.args,
    defaultValue: 'Read-only input',
    readOnly: true,
  },
  parameters: {
    docs: {
      description: {
        story: '編集できない入力フィールド。',
      },
    },
  },
};

export const PasswordInput: StoryT = {
  args: {
    ...Default.args,
    type: 'password',
    placeholder: 'Password Input',
  },
  parameters: {
    docs: {
      description: {
        story: 'テキストを隠すパスワードフィールド用の入力。',
      },
    },
  },
};

export const InputWithClear: StoryT = {
  args: {
    ...Default.args,
    allowClear: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'コンテンツを簡単に消去できるクリアボタン付きの入力。',
      },
    },
  },
};

export const InputWithCount: StoryT = {
  args: {
    ...Default.args,
    maxLength: 20,
    showCount: true,
  },
  parameters: {
    docs: {
      description: {
        story: '文字数を表示する入力。',
      },
    },
  },
};

export const SearchInputWithSearchButton: StoryObj<typeof InputSearch> = {
  render: (args: any) => <ThemedSearch {...args} />,
  args: {
    placeholder: 'Search...',
    enterButton: 'Search',
    onSearch: (value: string) => console.info(value),
  },
  parameters: {
    docs: {
      description: {
        story: '表示されている検索ボタン付きの検索入力。',
      },
    },
  },
};

export const SearchInputOnEnter: StoryObj<typeof InputSearch> = {
  render: (args: any) => <ThemedSearch {...args} />,
  args: {
    placeholder: 'Search...',
    onSearch: (value: string) => console.info(value),
    enterButton: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Enterキーを押すと検索がトリガーされる検索入力。',
      },
    },
  },
};
