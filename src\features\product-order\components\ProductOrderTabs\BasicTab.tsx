import { Form } from '@/components/organisms/Form';
import { Fragment } from 'react';

interface BasicTabPropsI {
  form: any;
  numOfColumns: number;
  formFields: any[];
}

const BasicTab = ({ form, numOfColumns, formFields }: BasicTabPropsI) => {
  return (
    <Fragment>
      <Form
        name="basic"
        form={form}
        numOfColumns={numOfColumns}
        formFields={formFields}
        style={{ marginTop: 45 }}
      />
    </Fragment>
  );
};

export default BasicTab;
