export enum RegexE {
  EMAIL = '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$',
  PHONE = '^\\(?\\d{3}\\)?[-.\\s]?\\d{3}[-.\\s]?\\d{4}$',
  URL = '^(https?:\\/\\/)?([\\w-]+\\.)+[\\w-]{2,}(\\/[\\w-./?%&=]*)?$',
  IPV4 = '^(25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]?\\d)(\\.(?!$)|$){4}$',
  HEX_COLOR = '^#?([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$',
  ZIP_CODE = '^\\d{5}(-\\d{4})?$',
  DATE = '^\\d{4}-\\d{2}-\\d{2}$',
  TIME_24H = '^([01]\\d|2[0-3]):([0-5]\\d)$',
  PASSWORD = '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[A-Za-z\\d]{8,}$',
  ONLY_LETTERS = '^[A-Za-z]+$',
  ONLY_NUMBERS = '^\\d+$',
  ALPHANUMERIC = '^[A-Za-z0-9]+$',
  TRIM_WHITESPACE = '^\\s+|\\s+$',
  HTML_TAG = '<[^>]*>',
  DECIMAL_NUMBER = '^\\d{1,10}(\\.\\d{1,2})?$', // Matches up to 10 digits before the decimal and up to 2 digits after
  DECIMAL_NUMBER_12_3 = '^\\d{1,12}(\\.\\d{1,3})?$',
  ONLY_NUMBERS_HALF = '^[0-9]+$',
}
