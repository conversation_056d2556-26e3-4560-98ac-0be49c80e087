{"productList": "Product Information List", "addRow": "Add row", "sizeInformationDetails": "Size information details", "noteCheckColorSize": "※Please check the colour/size you would like to order.", "update": "Update", "no": "No", "color": "Color", "size": "Size", "order": "order {{count}}", "delete": "Delete", "lastUpdated": "Last updated", "newRegistrationDateAndTime": "New registration date and time", "partNumber": "Part Number", "productName": "Product Name", "beforeChange": "【Before Change】", "afterChange": "【After Change】", "pleaseEnterRequiredFields": "Please enter required fields", "noDataFound": "No data found", "updateFailed": "Update Failed", "updateCompleted": "Update Completed", "deleteCompleted": "Delete completed", "return": "Return", "basicInfo": "Basic", "colorSize": "Color/Size", "orderInfo": "Order Information List", "productCode": "Product Code", "genderCategory": "Gender Category", "productCategory": "Product Category", "storeBudgetCategory": "Store Budget Category", "storeBudgetCategoryDescription": "*The allocation rate will be determined based on the store budget amount for this category.", "salesPeriod": "Sales Period", "storeArrivalDate": "Store Arrival Date", "salesSeasonCode": "Sales Season Code", "salesSeasonCodeDescription": "*The sales season code will be automatically generated based on the 'planned sales period' and 'planned in-store allocation date.'", "sampleNumber": "Sample Number", "buyer": "Buyer", "hsCode": "HS Code", "notes": "Notes", "required": "Required", "startDate": "start Date", "endDate": "end Date", "registration": "Registration", "productDetail": "Product information details", "productInformationList": "Product Information List", "productInformationAddition": "Add Product Information", "productCodeNotesInput": "Enter product number, product name, and notes", "detailedSearch": "Detailed Search", "pagerTotalCount": "Pager / Total Count", "image": "Image", "productNo": "Product number", "planner": "Planner", "gender": "Gender", "category": "Category", "remarks": "Remarks", "productNameSearch": "Product Name (partial match)", "productionCategory": "Production Category", "partner": "Partner", "deliveryPeriodPlanned": "Designated shipping date (planned)", "deliveryPeriodActual": "Designated shipping date (actual)", "remarksSearch": "Remarks (partial match)", "productClassification": "Product classification", "fabricNo": "<PERSON><PERSON><PERSON>", "fabricNameSearch": "Fabric name (partial match)", "orderType": "Order type", "storeArrivalPlanDatetime": "Store arrival plan", "storeArrivalResultDatetime": "Store arrival result", "formSearchError": "There is an error in the input. Please check", "multipleInputHint": "※If specifying multiple entries, separate them with line breaks.", "clear": "Clear", "advancedSearch": "Advanced Search", "startDateMustBeLessOrEqualEndDate": "Start date must be less than or equal to end date", "productClassificationSelection": "Product Classification Selection", "productClassificationSearchPlaceholder": "Enter product classification group name, product classification name", "productClassificationGroup": "Product Classification Group", "selection": "Selection", "select": "Select", "search": "Search", "choise": "<PERSON><PERSON>", "codeValue": "Code Value", "codeName": "Code Name", "enterValueAndCode": "Enter the code value and code name", "selectCodeInformation": "Select Code Information (Product Category)", "purchaseOrderSubmission": "Purchase Order submission", "addOrderingInformation": "Add ordering information", "numberOfOrders": "Number of orders", "orderDate": "Order date", "businessPartner": "Business Partner", "retailPriceIncludingTax": "Retail price including tax", "cost": "Cost", "purchaseOrder": "Purchase order", "specification": "specification", "lastSentDateAndTime": "Last sent date and time", "numberOfShipments": "Number of shipments", "localPort": "Local Port", "shippingDate": "Shipping date", "japanStore": "Japan Store", "arrivalDate": "Arrival Date", "exchange": "Exchange", "rate": "rate", "shipmentQuantity": "Shipment Quantity", "shippingAmount": "Shipping amount", "orderTimes": "{{count}}times", "Unregistered": "Unregistered", "Unsent": "Unsent", "well": "Well", "lang": "<PERSON>", "productCategorySettings": "Product Category Settings", "addProductCategory": "Add Product Category", "productCodeOrItemCode": "Product Code / Item Code", "copyProductCategorySettings": "Copy Product Category Settings", "copy": "Copy", "productCategoryGroup": "Product Category Group", "productCategoryTable": "Product Category", "nameStoreBudget": "Name", "storeBudgetCategorySelection": "Store budget category selection", "colorRequiredFields": "Please enter a color.", "sizeRequiredFields": "Please enter your size.", "askUpdate": "Do you want to update your information?", "askDelete": "Are you sure you want to delete your information?", "inputRequired": "{{ name<PERSON>ield }} is a required field."}