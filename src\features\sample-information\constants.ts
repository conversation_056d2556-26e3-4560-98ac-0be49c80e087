import { TFunction } from 'i18next';

export const GenderCategory = {
  men: 'M',
  woman: 'F',
  kidsBoys: 'KIDS(BOYS)',
  kidsGirls: 'KIDS(GIRLS)',
} as const;

export const PAGE_SIZE = 10;

export const getGenderCategoryOptions = (
  t: TFunction<'sample-information'>,
) => [
  { label: '', value: '' },
  { label: t('genders.men'), value: GenderCategory.men },
  { label: t('genders.woman'), value: GenderCategory.woman },
  { label: t('genders.kids_boys'), value: GenderCategory.kidsBoys },
  { label: t('genders.kids_girls'), value: GenderCategory.kidsGirls },
];
