import { Image, Text } from '@/components/atoms';
import { AppRouteE } from '@/enums';
import { QueryKeysE } from '@/enums/query-keys';
import {
  ProductI,
  ProductSearchFormValuesI,
} from '@/features/product-information/types';
import { Flex, message } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useTranslation } from 'next-i18next';
import Link from 'next/link';
import { useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { ProductFormFieldKeyE } from '../enums';
import { getProducts } from '../services';

export const useProductInformation = (
  page: number,
  pageSize: number,
  setDefaultPage: () => void,
) => {
  const { t } = useTranslation('products');

  const [searchKeyword, setSearchKeyword] = useState<string>('');
  useState<ProductSearchFormValuesI>({});
  const [filters, setFilters] = useState<Record<string, any>>({});

  const columns: ColumnsType<ProductI> = useMemo(
    () => [
      {
        title: t('no'),
        key: ProductFormFieldKeyE.NO,
        width: 50,
        align: 'center',
        render: (_: any, _record: ProductI, index: number) =>
          (page - 1) * pageSize + index + 1,
        className: 'first-column-bg',
      },
      {
        title: t('image'),
        key: ProductFormFieldKeyE.PRODUCT_IMAGE,
        width: 120,
        align: 'center',
        fixed: 'left',
        render: (_: any, record: ProductI) => (
          <Flex justify="center" align="center">
            <Image
              width={80}
              height={80}
              src={record.product_media?.media_file?.media_file_path}
              alt="product_image"
            />
          </Flex>
        ),
      },
      {
        title: t('productNo'),
        dataIndex: ProductFormFieldKeyE.PRODUCT_NO,
        key: ProductFormFieldKeyE.PRODUCT_NO,
        width: 150,
        align: 'center',
        render: (value: string, record: ProductI) => (
          <Link
            style={{
              color: 'var(--link-text-table)',
              textDecoration: 'underline',
            }}
            href={`/${AppRouteE.PRODUCTS}/${record.id}`}
            target="_blank"
          >
            {value}
          </Link>
        ),
      },
      {
        title: t('productName'),
        dataIndex: ProductFormFieldKeyE.PRODUCT_NAME,
        key: ProductFormFieldKeyE.PRODUCT_NAME,
        width: 300,
        align: 'center',
        render: (value: string) => (
          <Text className="max-3-rows-truncate text-left text-sm">
            {value}{' '}
          </Text>
        ),
      },
      {
        title: t('sampleNumber'),
        dataIndex: ProductFormFieldKeyE.SAMPLE_NO,
        key: ProductFormFieldKeyE.SAMPLE_NO,
        width: 150,
        align: 'center',
        render: (value: string) => (
          <Text className="max-3-rows-truncate text-sm">{value}</Text>
        ),
      },
      {
        title: t('planner'),
        dataIndex: ProductFormFieldKeyE.PLANNER,
        key: ProductFormFieldKeyE.PLANNER,
        width: 120,
        align: 'center',
        render: (value: string) => (
          <Text className="max-3-rows-truncate text-left text-sm">{value}</Text>
        ),
      },
      {
        title: t('gender'),
        dataIndex: ProductFormFieldKeyE.GENDER_CATEGORY,
        key: ProductFormFieldKeyE.GENDER_CATEGORY,
        width: 100,
        align: 'center',
        render: (value: string) => (
          <Text className="max-3-rows-truncate text-left text-sm">{value}</Text>
        ),
      },
      {
        title: t('category'),
        dataIndex: ProductFormFieldKeyE.PRODUCT_CATEGORY,
        key: ProductFormFieldKeyE.PRODUCT_CATEGORY,
        width: 120,
        align: 'center',
        render: (value: string) => (
          <Text className="max-3-rows-truncate text-left text-sm">{value}</Text>
        ),
      },
      {
        title: t('remarks'),
        dataIndex: ProductFormFieldKeyE.REMARKS,
        key: ProductFormFieldKeyE.REMARKS,
        width: 250,
        align: 'center',
        render: (value: string) => (
          <Text className="max-3-rows-truncate text-left text-sm">{value}</Text>
        ),
      },
    ],
    [t, page, pageSize],
  );

  const query = useQuery(
    [QueryKeysE.PRODUCTS, page, pageSize, filters, searchKeyword],
    () =>
      getProducts(page, pageSize, {
        ...filters,
        search: searchKeyword,
      }),
    {
      onSuccess: (data) => {
        if (!data?.data?.length) {
          message.warning(t('noDataFound'));

          return;
        }
      },
    },
  );

  const applyFilters = (
    keyword: string,
    advancedFilters: Record<string, any>,
    mode: 'normal' | 'advanced' = 'normal',
  ) => {
    setDefaultPage();
    if (mode === 'normal') {
      setSearchKeyword(keyword.trim());
      setFilters({});
    } else {
      setSearchKeyword('');
      setFilters(advancedFilters);
    }
  };

  return {
    ...query,
    columns,
    applyFilters,
  };
};
