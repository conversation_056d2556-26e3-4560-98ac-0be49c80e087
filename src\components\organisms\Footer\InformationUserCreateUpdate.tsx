import { Button, Text } from '@/components/atoms';
import { InformationUserCreateUpdateI } from '@/types/common';
import { DateHelper } from '@/utils/date';
import { Fragment, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

const InformationUserCreateUpdate = ({
  type,
  handleSubmit,
  handleDelete,
  data,
}: {
  type: string;
  handleSubmit: () => void;
  handleDelete: () => void;
  data: InformationUserCreateUpdateI;
}) => {
  const { t } = useTranslation('common');

  const { createdAt, updatedAt } = useMemo(() => {
    return {
      createdAt: data?.created_at
        ? DateHelper.formatString(data.created_at)
        : '',
      updatedAt: data?.updated_at
        ? DateHelper.formatString(data.updated_at)
        : '',
    };
  }, [data?.created_at, data?.updated_at]);

  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <Fragment>
      <div className="mt-4 flex gap-2">
        <Button
          type="primary"
          label={
            mounted ? (type === 'edit' ? t('update') : t('registration')) : ''
          }
          onClick={() => handleSubmit()}
        />
        <Button
          hidden={type !== 'edit'}
          type="primary"
          danger
          label={mounted ? t('delete') : ''}
          onClick={() => handleDelete()}
        />
      </div>
      {type === 'edit' && (
        <Text variant="caption" style={{ fontSize: 14 }}>
          {t('newRegistrationDateAndTime')}: {createdAt}{' '}
          {data?.created_by?.name ? `(${data?.created_by?.name})` : ''}
          <br />
          {t('lastUpdated')}: {updatedAt}{' '}
          {data?.updated_by?.name ? `(${data?.updated_by?.name})` : ''}
        </Text>
      )}
    </Fragment>
  );
};

export default InformationUserCreateUpdate;
