#!/bin/bash

source "$(dirname "$0")/utils.sh"

run_step() {
    local message="$1"
    local command="$2"

    print_success "🔄 $message"
    if eval "$command"; then
        print_success "✅ $message completed successfully"
    else
        print_error "❌ $message failed"
        exit 1
    fi
}

run_step "Check lint" "yarn lint"
run_step "Format code" "yarn format"

# Check for changes after formatting
if ! git diff --exit-code >/dev/null; then
    print_warning "⚠️  Code changes detected after formatting. Please commit again!"
fi
