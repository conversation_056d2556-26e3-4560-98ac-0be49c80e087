import { apiRequest } from '@/api/api-request';
import { UploadedFileI } from '@/components/organisms/FileUploadList';

export const updateFiles = async (
  files: UploadedFileI[],
  selectedKeys: string[],
  url: string,
) => {
  const payload = files.map((file) => ({
    path: file.path,
    original_name: file.original_name,
    logical_name: file.logical_name,
    flag: selectedKeys.includes(file.original_name),
  }));

  const res = await apiRequest.post(url, payload);

  return res.data;
};
