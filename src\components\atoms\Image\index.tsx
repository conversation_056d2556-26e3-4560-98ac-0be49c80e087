import { Skeleton } from 'antd';
import NextImage, { ImageProps } from 'next/image';
import React, { useState } from 'react';

export const Image = (props: ImageProps) => {
  const { className, style, onLoadingComplete, width, height, ...rest } = props;
  const [isLoaded, setIsLoaded] = useState(false);

  const handleLoadingComplete: ImageProps['onLoadingComplete'] = (result) => {
    setIsLoaded(true);
    onLoadingComplete?.(result);
  };

  const skeletonStyle: React.CSSProperties = {
    width: typeof width === 'number' ? width : (width ?? '100%'),
    height: typeof height === 'number' ? height : (height ?? '100%'),
    aspectRatio: width && height ? `${width} / ${height}` : 'auto',
  };

  return (
    <div
      style={{
        lineHeight: 0,
        ...style,
      }}
      className={className}
    >
      {!isLoaded && (
        <Skeleton.Avatar shape="square" active style={skeletonStyle} />
      )}

      <NextImage
        {...rest}
        width={isLoaded ? width : 1}
        height={isLoaded ? height : 1}
        onLoadingComplete={handleLoadingComplete}
        className={className}
        style={{
          opacity: isLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease',
          aspectRatio: width && height ? `${width} / ${height}` : 'auto',
          ...style,
        }}
      />
    </div>
  );
};
