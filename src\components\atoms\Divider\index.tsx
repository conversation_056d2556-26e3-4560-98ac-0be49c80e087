import type { DividerProps } from 'antd';
import { Divider as AntDivider } from 'antd';
import classNames from 'classnames';
import React from 'react';

interface DividerPropsI extends DividerProps {
  borderColor?: string;
}

export const Divider: React.FC<DividerPropsI> = ({
  borderColor = 'var(--divider-color)',
  className,
  style,
  ...rest
}) => {
  return (
    <AntDivider
      className={classNames('!my-10', className)}
      style={{ borderTop: `1px solid ${borderColor}`, ...style }}
      {...rest}
    />
  );
};
