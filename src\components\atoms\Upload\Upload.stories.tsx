import { withThemeProvider } from '@/hocs/withThemeProvider';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Meta, StoryObj } from '@storybook/react-vite';
import { Button } from '../Button';
import { Upload } from './index';

const ThemedButton = withThemeProvider(Button);
const ThemedUploadDragger = withThemeProvider(Upload.Dragger);
const ThemedUpload = withThemeProvider(Upload);

const meta: Meta<typeof Upload> = {
  title: 'Atoms/Upload',
  component: ThemedUpload,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'アップロードコンポーネントを使用すると、ユーザーはローカルマシンからファイルを選択してアップロードできます。ボタンをクリックするか、指定された領域にファイルをドラッグアンドドロップすることでトリガーできます。このコンポーネントはアップロードの進行状況に関するフィードバックを提供し、さまざまなファイルタイプとサイズをサポートするようにカスタマイズできます。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Upload>;

export const Default: StoryT = {
  args: {
    children: (
      <ThemedButton icon={<UploadOutlined />} label="Click to Upload" />
    ),
  },
  parameters: {
    docs: {
      description: {
        story:
          'ボタンをクリックすることでトリガーされるデフォルトのアップロードコンポーネント。',
      },
    },
  },
};

export const PictureWall: StoryT = {
  args: {
    listType: 'picture-card',
    children: (
      <div>
        <PlusOutlined />
        <div style={{ marginTop: 8 }}>Upload</div>
      </div>
    ),
    defaultFileList: [
      {
        uid: '-1',
        name: 'image.png',
        status: 'done',
        url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
      },
    ],
  },
  parameters: {
    docs: {
      description: {
        story:
          'このストーリーは、画像ギャラリーに最適な、ピクチャーウォールとしてスタイル設定されたアップロードコンポーネントを示しています。アップロードされたアイテムがどのように表示されるかを示すためのデフォルトのファイルリストが含まれています。',
      },
    },
  },
};

export const DragAndDrop: StoryT = {
  args: {
    children: (
      <ThemedUploadDragger>
        <p className="text-2xl text-gray-400">
          <UploadOutlined />
        </p>
        <p className="font-semibold">
          Click or drag file to this area to upload
        </p>
        <p className="text-gray-500">Support for a single or bulk upload.</p>
      </ThemedUploadDragger>
    ),
  },
  parameters: {
    docs: {
      description: {
        story:
          'このストーリーは、ドラッグアンドドロップ機能を示しており、ユーザーは指定された領域にファイルをドラッグすることでファイルをアップロードできます。',
      },
    },
  },
};

export const Avatar: StoryT = {
  args: {
    listType: 'picture-circle',
    showUploadList: false,
    children: (
      <div>
        <PlusOutlined />
        <div style={{ marginTop: 8 }}>Upload</div>
      </div>
    ),
  },
  parameters: {
    docs: {
      description: {
        story:
          'このストーリーは、アバターをアップロードするためにアップロードコンポーネントがどのように使用できるかを示しています。円形にスタイル設定されており、ファイルリストは非表示になっています。',
      },
    },
  },
};
