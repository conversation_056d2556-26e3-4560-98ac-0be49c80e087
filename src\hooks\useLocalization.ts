import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

const useLocalization = () => {
  const router = useRouter();
  const { t: tCommon, i18n } = useTranslation('common');
  const { t: tComponents } = useTranslation('components');

  const changeLanguage = (newLocale: string) => {
    router.push(router.pathname, router.asPath, { locale: newLocale });
    i18n.changeLanguage(newLocale);
  };

  return {
    changeLanguage,
    tCommon,
    tComponents,
    router,
    currentLocale: i18n.language,
  };
};

export default useLocalization;
