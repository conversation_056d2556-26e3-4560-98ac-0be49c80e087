import type { Meta, StoryObj } from '@storybook/react-vite';
import ImageUploadList from './index';

const meta = {
  title: 'Organisms/ImageList',
  component: ImageUploadList,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof ImageUploadList>;

export default meta;

type StoryT = StoryObj<typeof ImageUploadList>;

export const Default: StoryT = {
  render: () => (
    <div style={{ width: '800px', padding: '20px' }}>
      <ImageUploadList />
    </div>
  ),
};

// Example with pre-loaded images can be added in the future if needed
