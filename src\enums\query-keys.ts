export enum QueryKeysE {
  FEATURE = 'feature',
  SAMPLES = 'samples',
  PRODUCTS = 'products',
  BUYER = 'buyers',
  GENDER_CATEGORY = 'genderCategory',
  PRODUCT_CATEGORY = 'productCategory',
  COLOR = 'color',
  SIZE = 'size',
  ORDER_TYPE = 'orderType',
  PRODUCT_CLASSIFICATION = 'productClassification',
  PARTNER = 'partner',
  SUPPLIER = 'supplier',
  SELECT_OPTIONS = 'selectOptions',
  STORE_BUDGET_TYPES = 'store-budget-types',
  PRODUCT_STORE_BUDGET_TYPES = 'product-store-budget-types',
  PRODUCT_ORDERS = 'productOrders',
}
