import { Select } from '@/components/atoms';
import { ApiEndpointE } from '@/enums/api-endpoints';
import { QueryKeysE } from '@/enums/query-keys';
import { SelectE } from '@/enums/select';
import { getSelectOptions } from '@/features/sample-information/services';
import { SelectProps as AntdSelectProps } from 'antd';
import { useMemo } from 'react';
import { useQuery } from 'react-query';

export interface SelectPropsI extends AntdSelectProps {
  apiConfig?: {
    endpoint?: string;
    params?: Record<string, any>;
    mapFields?: {
      label: string;
      value: string;
    };
  };
}

export const MasterDataDropdown = (props: SelectPropsI) => {
  const { apiConfig } = props;
  const endpoint = apiConfig?.endpoint || ApiEndpointE.MASTER_CODE;
  const mapFields = apiConfig?.mapFields || {
    label: SelectE.M_CODE_FIELD_CODE_NAME,
    value: SelectE.M_CODE_FIELD_CODE,
  };

  const query = useQuery(
    [QueryKeysE.SELECT_OPTIONS, apiConfig],
    async () => {
      const data = await getSelectOptions(String(endpoint), apiConfig?.params);
      if (Array.isArray(data?.result) && mapFields) {
        return data.result.map((item: any) => ({
          label: item[mapFields.label],
          value: item[mapFields.value],
        }));
      }
    },
    { enabled: !!apiConfig },
  );

  const options = useMemo(
    () => query.data || props.options || [],
    [query.data, props.options],
  );

  return <Select {...props} options={options} />;
};
