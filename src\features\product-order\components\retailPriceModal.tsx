import { Button, InputSearch } from '@/components/atoms';
import { Table } from '@/components/atoms/Table';
import usePagination from '@/hooks/usePagination';
import { SearchOutlined } from '@ant-design/icons';
import { Radio as AntdRadio, Modal } from 'antd';
import { useTranslation } from 'next-i18next';
import React, { useCallback, useState } from 'react';
import { useRetailPriceModal } from '../hooks/useRetailPriceModal';
import { RetailPriceI } from '../types';

interface RetailPriceModalPropsI {
  onSelect?: (selected: RetailPriceI) => void;
  isOpen: boolean;
  onClose: () => void;
  modalTitle: string;
}

const RetailPriceModal: React.FC<RetailPriceModalPropsI> = ({
  onSelect,
  isOpen,
  onClose,
  modalTitle,
}) => {
  const { t } = useTranslation(['product-order', 'common']);
  const [keyword, setKeyword] = useState('');
  const { pagination } = usePagination();
  const [selectedCode, setSelectedCode] = useState<string | null>('');

  const { data, isFetching, columns, refetch, applyFilters } =
    useRetailPriceModal(pagination.current, pagination.pageSize);

  const handleSearch = useCallback(() => {
    applyFilters(keyword);
  }, [keyword, applyFilters]);

  const handleConfirmSelect = () => {
    const selectedItem = data?.result?.find(
      (item: RetailPriceI) => item.code === selectedCode,
    );
    if (selectedItem && onSelect) {
      onSelect({
        id: selectedItem.id,
        code: selectedItem.code,
        code_name: selectedItem.code_name,
      });
    }
  };

  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={900}
      title={`${t('chordInformationSelection')} (${modalTitle})`}
    >
      <div style={{ marginTop: 30, marginBottom: 32 }}>
        <InputSearch
          allowClear
          placeholder={t('common:enterNameAndCode')}
          enterButton={t('common:search')}
          value={keyword}
          prefix={<SearchOutlined />}
          maxLength={100}
          onChange={(e) => setKeyword(e.target.value)}
          onSearch={handleSearch}
        />
      </div>

      <AntdRadio.Group
        className="w-full"
        value={selectedCode}
        onChange={(e) => setSelectedCode(e.target.value)}
      >
        <Table<RetailPriceI>
          columns={columns}
          dataSource={data?.result}
          rowKey="id"
          pagination={{
            ...pagination,
            total: data?.meta?.total || 0,
          }}
          size="small"
          scroll={{ y: 300 }}
          loading={isFetching}
          onRefresh={refetch}
          loadingMinHeight="20px"
        />
      </AntdRadio.Group>

      <div style={{ marginTop: 24, textAlign: 'left' }}>
        <Button
          type="primary"
          label={t('common:select')}
          onClick={handleConfirmSelect}
        />
      </div>
    </Modal>
  );
};

export default RetailPriceModal;
