import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { Meta, StoryObj } from '@storybook/react-vite';
import { Image as AntdImage, message } from 'antd';
import { Image } from './index';
const ThemedPreviewGroup = withThemeProvider(AntdImage.PreviewGroup);

const ThemedImage = withThemeProvider(Image);

const meta: Meta<typeof Image> = {
  title: 'Atoms/Image',
  component: ThemedImage,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'Imageコンポーネントは、アプリケーションに画像を表示するために使用されます。画像プレビュー、キャプション、画像の読み込みに失敗した場合のフォールバックサポートなど、ユーザーエクスペリエンスを向上させる一連の機能を提供します。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Image>;

export const Default: StoryT = {
  args: {
    width: 200,
    src: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
  },
  parameters: {
    docs: {
      description: {
        story: 'これは画像コンポーネントのデフォルトの状態です。',
      },
    },
  },
};

export const WithAltText: StoryT = {
  args: {
    width: 200,
    src: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
    alt: 'Alipay Logo',
  },
  parameters: {
    docs: {
      description: {
        story: 'アクセシビリティのための代替テキスト付きの画像。',
      },
    },
  },
};

export const WithFallback: StoryT = {
  args: {
    width: 200,
    src: 'https://not-a-real-url.com/image.png',
  },
  parameters: {
    docs: {
      description: {
        story:
          'メイン画像の読み込みに失敗した場合のフォールバック画像付きの画像。',
      },
    },
  },
};

export const WithPlaceholder: StoryT = {
  args: {
    width: 200,
    src: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
  },
  parameters: {
    docs: {
      description: {
        story: 'ローディングプレースホルダー付きの画像。',
      },
    },
  },
};

export const PreviewDisabled: StoryT = {
  args: {
    width: 200,
    src: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
  },
  parameters: {
    docs: {
      description: {
        story: 'プレビューモーダルが無効な画像。',
      },
    },
  },
};

export const CustomSize: StoryT = {
  args: {
    width: 300,
    height: 150,
    src: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
  },
  parameters: {
    docs: {
      description: {
        story: 'カスタムの幅と高さの画像。',
      },
    },
  },
};

// Preview Group Example

export const PreviewGroup: StoryObj = {
  render: () => (
    <ThemedPreviewGroup>
      <Image
        width={100}
        src="https://images.unsplash.com/photo-1506744038136-46273834b3fb"
        alt="A beautiful landscape"
      />
      <Image
        width={100}
        src="https://images.unsplash.com/photo-1519125323398-675f0ddb6308"
        alt="A winding road"
      />
      <Image
        width={100}
        src="https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e"
        alt="A person with a camera"
      />
    </ThemedPreviewGroup>
  ),
  parameters: {
    docs: {
      description: {
        story: 'プレビューグループ内の複数画像。',
      },
    },
  },
};

export const CustomPreviewConfig: StoryT = {
  args: {
    width: 200,
    src: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
  },
  parameters: {
    docs: {
      description: {
        story:
          'カスタムプレビュー構成（カスタムマスクとスケールステップ）の画像。',
      },
    },
  },
};

export const OnErrorCallback: StoryT = {
  args: {
    width: 200,
    src: 'https://not-a-real-url.com/image.png',
    onError: () => {
      message.error('Image failed to load!');
    },
  },
  parameters: {
    docs: {
      description: {
        story: '読み込み失敗を処理するためのonErrorコールバック付きの画像。',
      },
    },
  },
};
