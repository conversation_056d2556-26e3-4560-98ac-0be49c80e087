import { Tabs as AntdTabs } from 'antd';
import { styled } from 'styled-components';

const StyledTabsCustom = styled(AntdTabs)<{ $isEdit?: boolean }>`
  .ant-tabs-nav {
    margin: 0;
  }

  .ant-tabs-tab {
    border-radius: 6px 6px 0 0 !important;
    border: 1px solid var(--divider-color);
    margin: unset !important;
    margin-right: 10px !important;
    margin-left: 10px;
    padding: 0 40px;
    height: 34px;
    position: relative;
    bottom: -1px;
  }

  .ant-tabs-tab:not(.ant-tabs-tab-active) {
    border-radius: 6px !important;
    background-color: var(--icon-sub-button) !important;
    color: var(--bg-white) !important;
  }

  .ant-tabs-tab-active {
    background: white;
    border-top: 3px solid var(--button-accent-text);
    height: 38px !important;
    border-bottom: 2px solid transparent !important;
    z-index: 2;
  }

  .ant-tabs-tab-active .ant-tabs-tab-btn {
    color: var(--button-accent-text) !important;
  }

  .ant-tabs-tab-active:before {
    content: '';
    border: 1px solid var(--divider-color);
    position: absolute;
    bottom: -2px;
    width: 9px;
    height: 9px;
    left: -9px;
    border-bottom-right-radius: 7px;
    border-width: 0 1px 1px 0;
    box-shadow: 2px 2px 0 var(--bg-white);
  }

  .ant-tabs-tab-active:after {
    content: '';
    border: 1px solid var(--divider-color);
    position: absolute;
    bottom: -2px;
    width: 9px;
    height: 9px;
    right: -9px;
    border-bottom-left-radius: 7px;
    border-width: 0 0 1px 1px;
    box-shadow: -2px 2px 0 var(--bg-white);
  }

  .ant-tabs-nav-list {
    padding-left: 21px;
  }
  .ant-tabs-nav-wrap {
    overflow: unset !important;
  }
  .ant-tabs-nav {
    border-bottom: 1px solid var(--divider-color);
  }

  .ant-tabs-tab:not(.ant-tabs-tab-active) {
    ${({ $isEdit }) =>
      $isEdit &&
      `
        background: var(--bg-white) !important;
        border: 1px solid var(--divider-color) !important;
        color: var(--nav-base)  !important;
        border-bottom: none;
        border-radius: 6px 6px 0 0;
      `}
  }
`;

export default StyledTabsCustom;
