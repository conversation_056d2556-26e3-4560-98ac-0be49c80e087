import { Checkbox, Text } from '@/components/atoms';
import { ColumnType } from 'antd/es/table';
import { useTranslation } from 'next-i18next';
import { ProductFormFieldKeyE } from '../enums';
import { TProductTypeI } from '../types';

export const useProductCategorySettingSection = (
  data: TProductTypeI[],
  setData: React.Dispatch<React.SetStateAction<TProductTypeI[]>>,
) => {
  const { t } = useTranslation('products');

  const columns: ColumnType<TProductTypeI>[] = [
    {
      title: t('no'),
      key: 'index',
      width: 30,
      render: (_text, _row, index) => index + 1,
      align: 'center',
      className: 'first-column-bg',
    },
    {
      title: t('delete'),
      key: 'delete',
      width: 100,
      align: 'center',
      render: (_, record, index) => (
        <Checkbox
          checked={record.is_checked || false}
          onChange={(e) => {
            const checked = e.target.checked;
            const newData = [...data];
            newData[index] = {
              ...newData[index],
              is_checked: checked,
            };
            setData(newData);
          }}
        />
      ),
    },
    {
      title: t('productCategoryGroup'),
      width: 300,
      key: ProductFormFieldKeyE.PRODUCT_TYPE_GROUP,
      align: 'center',
      render: (record) => (
        <Text className="text-left">
          {record.product_type_fkid?.product_type_group
            ?.pms_product_type_group_name ?? ''}
        </Text>
      ),
    },
    {
      title: t('productCategoryTable'),
      minWidth: 120,
      key: ProductFormFieldKeyE.PRODUCT_TYPE_NAME,
      align: 'center',
      render: (record) => (
        <Text className="text-left">
          {record.product_type_fkid?.pms_product_type_name ?? ''}
        </Text>
      ),
    },
  ];

  return {
    columns,
  };
};
