import { ApiResponseDetailI } from '@/types/api';
import { ProductOrderFormFieldKeyE } from './enums';
import { ProductOrderDetailI, SupplierFactoryI } from './types';

interface MockResponseI {
  status: number;
  result: ProductOrderDetailI;
}

export const mockResponse: MockResponseI = {
  status: 200,
  result: {
    id: 1,
    [ProductOrderFormFieldKeyE.PMS_ORDER_SBT_CODE]: 'CODE1234',
    [ProductOrderFormFieldKeyE.PMS_ORDER_DATETIME]:
      '2019-08-27T13:27:02.000000Z',
    [ProductOrderFormFieldKeyE.PMS_JODAI_TAX_OUT]: '1,536',
    [ProductOrderFormFieldKeyE.PMS_JODAI_TAX_IN]: '1,676',
    [ProductOrderFormFieldKeyE.PMS_ORDER_CALC_RATE]: '1',
    [ProductOrderFormFieldKeyE.COUNTRY_ORIGIN_CODE]: 'CODE1234',
    [ProductOrderFormFieldKeyE.IMPORT_METHOD_CODE]: 'CODE1234',
    [ProductOrderFormFieldKeyE.IMPORT_COST_UNIT_CODE]: 'CODE1234',
    [ProductOrderFormFieldKeyE.PAYMENT_KGN]: '1,536',
    [ProductOrderFormFieldKeyE.PMS_MANAGER_CODE]: 'CODE1234',
    [ProductOrderFormFieldKeyE.SUPPLIER_FILTER]: 'CODE1234',
    [ProductOrderFormFieldKeyE.PMS_SUPPLIER_CODE]: 'SUP096',
    [ProductOrderFormFieldKeyE.PMS_SUPPLIER_FACTORY_CODE]: 'SUP096',
    [ProductOrderFormFieldKeyE.INSPECT_PRE_FLG]: true,
    [ProductOrderFormFieldKeyE.INSPECT_PRE_STAFF_CODE]: 'USER1',
    [ProductOrderFormFieldKeyE.INSPECT_PRE_DATETIME]:
      '2019-08-27T13:27:02.000000Z',
    [ProductOrderFormFieldKeyE.INSPECT_PRE_REMARKS]:
      'キッズ 配色ステッチ長袖半袖Tシャツ',
    [ProductOrderFormFieldKeyE.PART_NUMBER]: '30-*********-10',
    [ProductOrderFormFieldKeyE.PRODUCT_NAME]:
      'キッズ 配色ステッチ長袖半袖Tシャツ',
    [ProductOrderFormFieldKeyE.SELLING_POINT]: '売りポイント',
    [ProductOrderFormFieldKeyE.OTHER_NOTES]: 'その他の注意事項',
    [ProductOrderFormFieldKeyE.COMMENT]: 'コメント',
    [ProductOrderFormFieldKeyE.PROCESSING_DATE]: '2024/12/04',
    [ProductOrderFormFieldKeyE.COMPANY_NAME]: '株式会社テスト',
    [ProductOrderFormFieldKeyE.CONTRACT_DATE]: '2024/12/04',
    [ProductOrderFormFieldKeyE.NOTE]: '契約ノート',
    created_at: '2019-08-27T13:27:02.000000Z',
    updated_at: '2019-08-27T13:27:02.000000Z',
    created_by: {
      name: 'SYSTEM システムサポート',
    },
    updated_by: {
      name: 'kenji_tsuboi 坪井 建治',
    },
    supplier_factories: [
      {
        id: 1,
        delete: false,
        pms_supplier_factory_code: '16-01',
        pms_supplier_name: '16-01安さん工場',
      },
      {
        id: 2,
        delete: false,
        pms_supplier_factory_code: '16-02',
        pms_supplier_name: '16-02安さん工場',
      },
      {
        id: 3,
        delete: false,
        pms_supplier_factory_code: '36-01',
        pms_supplier_name: '36-07USAコットン工場',
      },
      {
        id: 4,
        delete: false,
        pms_supplier_factory_code: '36-08',
        pms_supplier_name: '36-08USAコットン工場',
      },
    ],
    purchase_prices: [
      {
        no: 1,
        color: '[White]',
        size: '[S]',
        price_tax_ex: 691,
        price_tax_in: 760,
        fob: 585,
      },
      {
        no: 2,
        color: '[White]',
        size: '[M]',
        price_tax_ex: 691,
        price_tax_in: 760,
        fob: 585,
      },
      {
        no: 3,
        color: '[White]',
        size: '[L]',
        price_tax_ex: 691,
        price_tax_in: 760,
        fob: 585,
      },
      {
        no: 4,
        color: '[Black]',
        size: '[S]',
        price_tax_ex: 691,
        price_tax_in: 760,
        fob: 585,
      },
      {
        no: 5,
        color: '[Black]',
        size: '[M]',
        price_tax_ex: 691,
        price_tax_in: 760,
        fob: 585,
      },
      {
        no: 6,
        color: '[Black]',
        size: '[L]',
        price_tax_ex: 691,
        price_tax_in: 760,
        fob: 585,
      },
    ],
    product_confirmations: {
      [ProductOrderFormFieldKeyE.PMS_PRODUCT_NO]: '30-*********-10',
      [ProductOrderFormFieldKeyE.PMS_PRODUCT_NAME]:
        'キッズ 配色ステッチ長袖半袖Tシャツ',
    },
    convey_msg_1: {
      [ProductOrderFormFieldKeyE.CONVEY_MSG]: '売りポイント',
    },
    convey_msg_2: {
      [ProductOrderFormFieldKeyE.CONVEY_MSG]: 'その他の注意事項',
    },
    convey_msg_3: {
      [ProductOrderFormFieldKeyE.CONVEY_MSG]: 'コメント',
    },
    convey_msg_4: {
      [ProductOrderFormFieldKeyE.CONVEY_MSG]: '2024/12/04',
    },
    convey_msg_5: {
      [ProductOrderFormFieldKeyE.CONVEY_MSG]: 'コメント',
    },
    contracts: [
      {
        no: 1,
        company_name: '株式会社テスト',
        contract_date: '2024/12/04',
        note: '契約ノート',
        created_at: '2019-08-27T13:27:02.000000Z',
        updated_at: '2019-08-27T13:27:02.000000Z',
        created_by: {
          name: 'SYSTEM システムサポート',
        },
        updated_by: {
          name: 'kenji_tsuboi 坪井 建治',
        },
      },
      {
        no: 2,
        company_name: '株式会社テスト',
        contract_date: '2024/12/04',
        note: 'キッズ 配色ステッチ長袖半袖Tシャツ',
        created_at: '2019-08-27T13:27:02.000000Z',
        updated_at: '2019-08-27T13:27:02.000000Z',
        created_by: {
          name: 'SYSTEM システムサポート',
        },
        updated_by: {
          name: 'kenji_tsuboi 坪井 建治',
        },
      },
      {
        no: 3,
        company_name: '株式会社テスト3',
        contract_date: '2024/12/04',
        note: 'キッズ 配色ステッチ長袖半袖Tシャツ',
        created_at: '2019-08-27T13:27:02.000000Z',
        updated_at: '2019-08-27T13:27:02.000000Z',
        created_by: {
          name: 'SYSTEM システムサポート',
        },
        updated_by: {
          name: 'kenji_tsuboi 坪井 建治',
        },
      },
    ],
  },
};

export const supplierFactoryData: SupplierFactoryI[] = [
  {
    id: 5,
    delete: false,
    pms_supplier_factory_code: '20-01',
    pms_supplier_name: '20-01安さん工場',
  },
  {
    id: 6,
    delete: false,
    pms_supplier_factory_code: '20-02',
    pms_supplier_name: '20-02安さん工場',
  },
  {
    id: 7,
    delete: false,
    pms_supplier_factory_code: '38-01',
    pms_supplier_name: '38-01USAコットン工場',
  },
  {
    id: 8,
    delete: false,
    pms_supplier_factory_code: '38-07',
    pms_supplier_name: '38-07USAコットン工場',
  },
];

export const getProductOrder = async (
  id: number,
): Promise<ApiResponseDetailI> => {
  return { ...mockResponse, ...{ id: id } };
};
