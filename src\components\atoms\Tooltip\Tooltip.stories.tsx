import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { Meta, StoryObj } from '@storybook/react-vite';
import { type TooltipPlacement } from 'antd/es/tooltip';
import { Button } from '../Button';
import { Tooltip } from './index';

const ThemedTooltip = withThemeProvider(Tooltip);

const meta: Meta<typeof Tooltip> = {
  title: 'Atoms/Tooltip',
  component: ThemedTooltip,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'ツールチップは、ユーザーがUI要素の上にカーソルを合わせたりフォーカスしたりしたときに、短い情報メッセージを表示する小さなポップアップです。ボタン、アイコン、リンクなどの要素に追加のコンテキストや説明を提供するために使用されます。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Tooltip>;

export const Default: StoryT = {
  args: {
    label: 'Tooltip',
    children: <Button label="Hover me" />,
  },
  parameters: {
    docs: {
      description: {
        story: 'これはツールチップコンポーネントのデフォルトの状態です。',
      },
    },
  },
};

const placements: TooltipPlacement[] = [
  'top',
  'left',
  'right',
  'bottom',
  'topLeft',
  'topRight',
  'bottomLeft',
  'bottomRight',
  'leftTop',
  'leftBottom',
  'rightTop',
  'rightBottom',
];

export const AllPlacements: StoryObj = {
  render: () => (
    <div className="grid grid-cols-4 gap-4">
      {placements.map((placement) => (
        <ThemedTooltip
          key={placement}
          label={placement}
          placement={placement}
          className="w-fit"
        >
          <Button label={placement} className="w-[120px]" />
        </ThemedTooltip>
      ))}
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          'このストーリーは、ツールチップで利用可能なすべての配置を示しています。',
      },
    },
  },
};
