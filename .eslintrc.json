{
  "ignorePatterns": [".next"],
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "next/core-web-vitals",
    "next/typescript",
    "plugin:storybook/recommended"
  ],
  "parserOptions": {
    "ecmaVersion": 2020
  },
  "env": {
    "browser": true
  },
  "plugins": ["@typescript-eslint", "filename-rules"],
  "overrides": [
    {
      "files": ["src/hooks/**/*.{ts,tsx}", "src/hocs/**/*.{ts,tsx}"],
      "excludedFiles": ["**/index.{ts,tsx}"],
      "rules": {
        "filename-rules/match": ["error", "camelCase"]
      }
    },
    {
      "files": ["src/components/**/*.{ts,tsx}"],
      "excludedFiles": [
        "**/index.{ts,tsx}",
        "**/styled.{ts,tsx}",
        "**/types.{ts,tsx}"
      ],
      "rules": {
        "filename-rules/match": ["error", "PascalCase"]
      }
    }
  ],
  "rules": {
    // "react-hooks/rules-of-hooks": "error",
    // "react-hooks/exhaustive-deps": "warn",
    // "react-refresh/only-export-components": [
    //   "warn",
    //   { "allowConstantExport": true }
    // ],
    "@typescript-eslint/no-explicit-any": "off",
    "no-empty-pattern": "off",
    "no-console": [
      "error",
      {
        "allow": ["info", "error"]
      }
    ],
    "newline-before-return": "error",
    "padding-line-between-statements": [
      "error",
      {
        "blankLine": "always",
        "prev": "*",
        "next": "return"
      },
      {
        "blankLine": "always",
        "prev": "function",
        "next": "function"
      },
      {
        "blankLine": "always",
        "prev": "directive",
        "next": "*"
      },
      {
        "blankLine": "always",
        "prev": "block-like",
        "next": "block-like"
      },
      {
        "blankLine": "always",
        "prev": "*",
        "next": "export"
      }
    ],
    "default-param-last": "off",
    "@typescript-eslint/default-param-last": "error",
    "no-empty-function": "off",
    "@typescript-eslint/no-empty-function": "error",
    "@typescript-eslint/no-array-constructor": "error",
    "@typescript-eslint/no-empty-interface": "warn",
    "@typescript-eslint/no-empty-object-type": "warn",
    "@typescript-eslint/no-for-in-array": "error",
    "@typescript-eslint/max-params": [
      "error",
      {
        "max": 3
      }
    ],
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/naming-convention": [
      "error",
      {
        "selector": "variable",
        "format": ["camelCase", "UPPER_CASE", "PascalCase", "snake_case"],
        "leadingUnderscore": "allow"
      },
      {
        "selector": "function",
        "format": ["camelCase", "PascalCase"],
        "leadingUnderscore": "allow"
      },
      {
        "selector": "property",
        "format": ["camelCase", "PascalCase", "snake_case"],
        "leadingUnderscore": "allow"
      },
      {
        "selector": "class",
        "format": ["PascalCase"]
      },
      {
        "selector": "method",
        "format": ["camelCase"]
      },
      {
        "selector": "parameter",
        "format": ["camelCase", "PascalCase"],
        "leadingUnderscore": "allow"
      },
      {
        "selector": "interface",
        "format": ["PascalCase"],
        "custom": {
          "regex": "^[A-Z]?[a-zA-Z0-9]*I$",
          "match": true
        }
      },
      {
        "selector": "typeAlias",
        "format": ["PascalCase"],
        "custom": {
          "regex": "^[A-Z]?[a-zA-Z0-9]*T$",
          "match": true
        }
      },
      {
        "selector": "enum",
        "format": ["PascalCase"],
        "custom": {
          "regex": "^[A-Z]?[a-zA-Z0-9]*E$",
          "match": true
        }
      },
      {
        "selector": "enumMember",
        "format": ["UPPER_CASE"]
      }
    ]
  }
}
