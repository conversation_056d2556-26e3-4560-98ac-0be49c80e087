import { Checkbox } from '@/components/atoms';
import React from 'react';
import { useDrag, useDrop } from 'react-dnd';

export interface ImageItemI {
  url: string;
  file: File;
  uid: string;
}

interface DraggableImageCardPropsI
  extends React.HTMLAttributes<HTMLDivElement> {
  index: number;
  image: ImageItemI;
  moveImage: (fromIndex: number, toIndex: number) => void;
  isChecked: boolean;
  onCheckChange: (id: string) => void;
  onClickCard: (id: string) => void;
}

interface DragItemI {
  index: number;
  type: string;
}

export const DraggableImageCard: React.FC<DraggableImageCardPropsI> = ({
  index,
  image,
  moveImage,
  isChecked,
  onCheckChange,
  onClickCard,
  ...restProps
}) => {
  const [, ref] = useDrop<DragItemI>({
    accept: 'image',
    hover(item, monitor) {
      if (!monitor.isOver() || !monitor.canDrop()) return;
      if (item.index === index) return;
      moveImage(item.index, index);
      item.index = index;
    },
  });

  const [{ isDragging }, drag] = useDrag<
    DragItemI,
    void,
    { isDragging: boolean }
  >({
    type: 'image',
    item: { index, type: 'image' },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  return (
    <div
      ref={(node) => {
        drag(ref(node));
      }}
      style={{
        opacity: isDragging ? 0.5 : 1,
        width: '100%',
        position: 'relative',
        cursor: 'move',
        overflow: 'hidden',
        height: '100%',
      }}
      className="rounded-md border-1 border-[var(--border-color)]"
      {...restProps}
    >
      <img
        alt={`Uploaded image ${index + 1}`}
        src={image.url}
        style={{
          height: '100%',
          objectFit: 'contain',
          width: '100%',
          maxHeight: 150,
        }}
        onClick={() => onClickCard?.(image.uid)}
      />

      <Checkbox
        checked={isChecked}
        onChange={() => onCheckChange(image.uid)}
        style={{ position: 'absolute', top: 10, right: 10 }}
        aria-label={`Toggle selection for image ${index + 1}`}
      />
    </div>
  );
};
