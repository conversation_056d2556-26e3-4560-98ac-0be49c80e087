import { Text } from '@/components/atoms/Text';
import { QueryKeysE } from '@/enums/query-keys';
import { ProductCategoryDataI } from '@/features/products/types';
import { CodeKbnE } from '@/features/sample-information/enums';
import { getMasterCode } from '@/services/common';
import { Radio as AntdRadio } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useTranslation } from 'next-i18next';
import { useMemo, useState } from 'react';
import { useQuery } from 'react-query';

export const useProductCategoryModal = (
  page: number,
  pageSize: number,
  setDefaultPage: () => void,
) => {
  const { t } = useTranslation('products');
  const [searchKeyword, setSearchKeyword] = useState<string>('');

  const query = useQuery(
    [QueryKeysE.PRODUCT_CATEGORY, page, pageSize, searchKeyword],
    () =>
      getMasterCode({
        code_kbn: [CodeKbnE.PRODUCT_CATERGORY],
        page,
        pageSize,
        searchKeyword,
      }),
    {
      onSuccess: (res) => {
        if (!res?.data?.length) {
          return;
        }
      },
      keepPreviousData: true,
    },
  );

  const applyFilters = (keyword: string) => {
    setDefaultPage();
    setSearchKeyword(keyword.trim());
  };

  const columns: ColumnsType<ProductCategoryDataI> = useMemo(
    () => [
      {
        title: 'No',
        key: 'no',
        align: 'center',
        width: 60,
        render: (
          _: ProductCategoryDataI,
          _record: ProductCategoryDataI,
          index: number,
        ) => (page - 1) * pageSize + index + 1,
        className: 'first-column-bg',
      },
      {
        title: t('choise'),
        width: 120,
        key: 'choise',
        align: 'center',
        render: (_value: unknown, record: ProductCategoryDataI) => (
          <AntdRadio value={record.code} />
        ),
      },
      {
        title: t('codeValue'),
        width: 120,
        key: 'code',
        align: 'center',
        render: (_value: unknown, record: ProductCategoryDataI) => (
          <Text className="text-left" variant="body2">
            {record.code}
          </Text>
        ),
      },
      {
        title: t('codeName'),
        key: 'code_name',
        align: 'center',
        render: (_value: unknown, record: ProductCategoryDataI) => (
          <Text className="text-left" variant="body2">
            {record.code_name}
          </Text>
        ),
      },
    ],
    [page, pageSize, t],
  );

  return {
    ...query,
    columns,
    applyFilters,
  };
};
