import { Text } from '@/components/atoms/Text';
import { QueryKeysE } from '@/enums/query-keys';
import { SelectE } from '@/enums/select';
import { getMasterCode } from '@/services/common';
import { Radio as AntdRadio } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useTranslation } from 'next-i18next';
import { useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { RetailPriceI } from '../types';

export const useRetailPriceModal = (
  page: number = 1,
  pageSize: number = 10,
) => {
  const { t } = useTranslation('common');
  const [searchKeyword, setSearchKeyword] = useState<string>('');

  const query = useQuery(
    [QueryKeysE.PRODUCT_CATEGORY, page, pageSize, searchKeyword],
    () =>
      getMasterCode({
        code_kbn: [SelectE.M_CODE_DROPDOWN_EXAMPLE],
        searchKeyword: searchKeyword,
        page,
        pageSize,
      }),
    {
      onSuccess: (res) => {
        if (!res?.data?.length) {
          return;
        }
      },
      keepPreviousData: true,
    },
  );

  const applyFilters = (keyword: string) => {
    setSearchKeyword(keyword.trim());
  };

  const columns: ColumnsType<RetailPriceI> = useMemo(
    () => [
      {
        title: 'No',
        key: 'no',
        align: 'center',
        width: 60,
        render: (_: RetailPriceI, _record: RetailPriceI, index: number) =>
          (page - 1) * pageSize + index + 1,
      },
      {
        title: t('select'),
        width: 120,
        key: 'select',
        align: 'center',
        render: (_value: unknown, record: RetailPriceI) => (
          <AntdRadio value={record.code} />
        ),
      },
      {
        title: t('codeValue'),
        width: 120,
        key: 'code',
        align: 'center',
        render: (_value: unknown, record: RetailPriceI) => (
          <Text className="text-left" variant="body2">
            {record.code}
          </Text>
        ),
      },
      {
        title: t('codeName'),
        key: 'code_name',
        align: 'center',
        render: (_value: unknown, record: RetailPriceI) => (
          <Text className="text-left" variant="body2">
            {record.code_name}
          </Text>
        ),
      },
    ],
    [page, pageSize, t],
  );

  const handleCloseModal = () => {
    setSearchKeyword('');
  };
  const [isOpen, setIsOpen] = useState(false);

  return {
    ...query,
    columns,
    applyFilters,
    handleCloseModal,
    isOpen,
    setIsOpen,
  };
};
