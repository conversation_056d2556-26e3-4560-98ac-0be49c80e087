import { Button, Table, Text } from '@/components/atoms';
import { Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import useColorSizeList from '../../hooks/useColorSizeList';
import { ProductDetailI, StoreBudgetTypeI } from '../../types';
import InfoProduct from './ViewTable';

interface ColorListPropsI {
  product?: ProductDetailI;
  dataTableMateria: Array<StoreBudgetTypeI>;
  setDataTableMateria: React.Dispatch<
    React.SetStateAction<Array<StoreBudgetTypeI>>
  >;
}

const ColorList = ({
  product,
  dataTableMateria,
  setDataTableMateria,
}: ColorListPropsI) => {
  const { t } = useTranslation('products');
  const { columns, isLoading, handleAddRow } = useColorSizeList({
    product,
    dataTableMateria,
    setDataTableMateria,
  });

  return (
    <Fragment>
      <div className="mt-10 flex w-full flex-col gap-4">
        <InfoProduct
          data={[
            {
              label: t('partNumber'),
              value: `${product?.pms_product_no || ''}`,
            },
            {
              label: t('productName'),
              value: `${product?.pms_remarks || ''}`,
            },
          ]}
        />
        <div className="flex w-full flex-col gap-1">
          <div className="flex items-center justify-between">
            <Text variant="span">{t('noteCheckColorSize')}</Text>
            <Button label={t('addRow')} type="primary" onClick={handleAddRow} />
          </div>
          <Table<StoreBudgetTypeI>
            columns={columns}
            dataSource={dataTableMateria}
            rowKey="id"
            loading={isLoading}
            loadingMinHeight="160px"
            pagination={false}
          />
        </div>
      </div>
    </Fragment>
  );
};

export default ColorList;
