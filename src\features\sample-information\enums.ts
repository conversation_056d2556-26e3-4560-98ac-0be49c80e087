export enum SampleFormFieldKeyE {
  SAMPLE_NUMBER = 'pms_sample_no',
  SAMPLE_NUMBERS = 'pms_sample_nos',
  BUYER = 'supplier_factory_code',
  GENDER_CATEGORY = 'gender_category_code',
  REQUEST_DATE = 'pms_sample_request_datetime',
  PRODUCT_CATEGORY = 'product_category_code',
  PLANNING_DATE = 'pms_plan_datetime',
  PARTNER = 'supplier_code',
  ORDER = 'order_flg',
  PREPARATION = 'pms_sample_remarks',
  RECEIPT = 'receipt_flg',
  EMPTY = 'empty',
  PAYMENT = 'payment_flg',
  PLANNER = 'pms_planner_code',
  COPY = 'copy',
  NO = 'no',
  IMAGE = 'image',
  REQUEST_DATE_FROM = 'pms_sample_request_datetime_from',
  REQUEST_DATE_TO = 'pms_sample_request_datetime_to',
  REFERENCE_URL = 'reference_url',
  QUOTE_KGN = 'quote_kgn',
  QUOTE_KGN_UNIT_CODE = 'quote_kgn_unit_code',
  PMS_SAMPLE_CREATE_CNT = 'pms_sample_create_cnt',
  PARTNER_FILTER = 'partner_filter',
  OPEN_PARTNER_MODEL = 'open_partner_model',
  PARTNER_FACTORY = 'partner_factory',
  ESTIMATE_PRICE_AND_CURRENCY = 'estimate_price_and_currency',
}

export enum CodeKbnE {
  BUYER = 'BUYER',
  GENDER_CATEGORY = 'GENDERCATEGORYCD',
  PRODUCT_CATERGORY = 'TMPCATECN',
}
