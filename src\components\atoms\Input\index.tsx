import { Input as AntdInput, InputProps as AntdInputProps } from 'antd';
import styled from 'styled-components';

export interface InputPropsI extends AntdInputProps {
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const StyledInput = styled(AntdInput)`
  box-shadow: var(--inner-box-shadow);
  border: 1px solid var(--border-color);

  ${(props) =>
    props.type === 'number' &&
    `
    &[type='number'] {
      -moz-appearance: textfield;
    }
    &[type='number']::-webkit-outer-spin-button,
    &[type='number']::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    .ant-input[type='number'] {
      -moz-appearance: textfield;
    }
    .ant-input[type='number']::-webkit-outer-spin-button,
    .ant-input[type='number']::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  `}
`;

type InputComponentT = React.FC<InputPropsI>;

export const Input: InputComponentT = ({
  value,
  onChange,
  ...props
}: InputPropsI) => {
  return (
    <StyledInput
      value={value}
      onChange={onChange}
      className="w-full"
      {...props}
    />
  );
};

export const InputSearch = styled(AntdInput.Search)`
  .ant-input {
    padding: 4px 11px !important;
    border: 1px solid var(--border-color);
  }

  .ant-input-affix-wrapper {
    box-shadow: var(--inner-box-shadow);
  }
  .ant-input-search-button {
    word-spacing: -3px;
  }
`;

export const InputPassword = styled(AntdInput.Password)`
  box-shadow: var(--inner-box-shadow);
  border: var(--boder);
`;
