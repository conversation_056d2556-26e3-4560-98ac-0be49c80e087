'use client';

import { TabsProps } from 'antd';
import dynamic from 'next/dynamic';

const StyledTabs = dynamic(() => import('./StyledTabsCustom'), {
  ssr: false,
});

const ClientOnlyStyledTabs = (props: TabsProps & { formType?: string }) => {
  const { formType, ...rest } = props;
  const isEdit = formType === 'edit';

  return <StyledTabs {...rest} $isEdit={isEdit} />;
};

export default ClientOnlyStyledTabs;
