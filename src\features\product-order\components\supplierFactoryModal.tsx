import { Button } from '@/components/atoms/Button';
import { Checkbox } from '@/components/atoms/Checkbox';
import { Form, FormFieldConfigI } from '@/components/organisms/Form';
import { CellComponentI } from '@/types/common';
import { Form as AntdForm, Modal } from 'antd';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import ReactDOM from 'react-dom/client';
import { ColumnDefinition } from 'react-tabulator';
import { ProductOrderFormFieldKeyE } from '../enums';
import { supplierFactoryData } from '../services';

interface SupplierFactoryModalPropsI {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any[]) => void;
}

export default function SupplierFactoryModal({
  isOpen,
  onClose,
  onSubmit,
}: SupplierFactoryModalPropsI) {
  const { t } = useTranslation(['product-order', 'common']);
  const [supplierFactorySearchForm] = AntdForm.useForm();

  const initialValues = {
    customer: '26-13無錫COCO',
    search_code: '',
    search_name: '',
  };

  const [dataTableSupplierFactory, setDataTableSupplierFactory] = useState(
    supplierFactoryData || [],
  );

  const supplierFactoryColumns: ColumnDefinition[] = [
    {
      title: 'No',
      field: ProductOrderFormFieldKeyE.ID,
      width: 50,
      headerHozAlign: 'center',
      hozAlign: 'center',
    },
    {
      title: t('common:delete'),
      width: 100,
      headerHozAlign: 'center',
      hozAlign: 'center',
      field: ProductOrderFormFieldKeyE.DELETE,
      formatter: (cell: CellComponentI) => {
        const record = cell.getRow().getData();
        const el = document.createElement('div');
        const value = cell.getValue();
        const root = ReactDOM.createRoot(el);
        root.render(
          <Checkbox
            defaultChecked={value}
            onChange={(e) =>
              handleCheckboxChange(record.id, 'delete', e.target.checked)
            }
          />,
        );

        return el;
      },
      cellClick: (e: Event, cell: CellComponentI) => {
        const target = e.target as HTMLElement;
        if (target?.classList?.contains('receipt-checkbox')) {
          const record = cell.getRow().getData();
          const checkbox = target as HTMLInputElement;
          handleCheckboxChange(record.id, 'delete', checkbox.checked);
        }
      },
    },
    {
      title: t('supplierFactoryCode'),
      field: ProductOrderFormFieldKeyE.PMS_SUPPLIER_FACTORY_CODE,
    },
    {
      title: t('supplierName'),
      field: ProductOrderFormFieldKeyE.PMS_SUPPLIER_NAME,
    },
  ];

  const handleSearchSupplierFactory = () => {
    // console.log('search supplier factory');
  };

  const handleSubmit = () => {
    onSubmit(dataTableSupplierFactory);
    onClose();
  };

  const handleCheckboxChange = (
    id: number,
    field: string,
    checked: boolean,
  ) => {
    setDataTableSupplierFactory((prevData) =>
      prevData.map((item) =>
        item.id === id ? { ...item, [field]: checked } : item,
      ),
    );
  };
  const searchFormFields: FormFieldConfigI[] = [
    {
      name: 'customer',
      type: 'input',
      label: t('common:customer'),
      disabled: true,
    },
    {
      name: 'search_code',
      type: 'input',
      label: t('supplierCode'),
    },
    {
      label: t('common:supplier'),
      name: ProductOrderFormFieldKeyE.SUPPLIER_FILTER,
      type: 'field-group',
      fields: [
        {
          name: 'search_name',
          type: 'input',
        },
        {
          name: t('common:search'),
          type: 'button',
          labelButton: t('common:search'),
          onClick: handleSearchSupplierFactory,
        },
      ],
    },
  ];

  const supplierFactoryFormFields: FormFieldConfigI[] = [
    {
      name: ProductOrderFormFieldKeyE.SUPPLIER_FACTORY,
      type: 'editable-table',
      editableTableConfig: {
        data: dataTableSupplierFactory,
        columns: supplierFactoryColumns,
        setData: setDataTableSupplierFactory,
      },
    },
  ];

  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={900}
      title={t('selectAFactory')}
    >
      <Form
        name="search-supplier-form"
        formFields={searchFormFields}
        initialValues={initialValues}
        form={supplierFactorySearchForm}
        style={{ marginTop: 30 }}
      />
      <Form
        name="supplier-factory-form"
        formFields={supplierFactoryFormFields}
      />

      <div style={{ textAlign: 'left', marginTop: 16 }}>
        <Button
          label="選択"
          type="primary"
          style={{
            background: '#FF6B6B',
            borderColor: '#FF6B6B',
            minWidth: 120,
          }}
          onClick={handleSubmit}
        />
      </div>
    </Modal>
  );
}
