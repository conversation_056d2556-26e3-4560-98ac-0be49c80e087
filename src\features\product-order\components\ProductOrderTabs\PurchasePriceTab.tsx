import { Button, EditableTable } from '@/components/atoms';
import { MasterDataDropdown } from '@/components/organisms/MasterDataDropdown';
import { SelectE } from '@/enums/select';
import { useTranslation } from 'next-i18next';

interface PurchasePriceTabPropsI {
  purchasePrice: any[];
  setPurchasePrice: (data: any[]) => void;
  purchasePriceColumns: any[];
  copyFirstPurchasePrice: () => void;
  copyPurchasePriceFromDropdown: () => void;
}

const PurchasePriceTab = ({
  purchasePrice,
  setPurchasePrice,
  purchasePriceColumns,
  copyFirstPurchasePrice,
  copyPurchasePriceFromDropdown,
}: PurchasePriceTabPropsI) => {
  const { t } = useTranslation(['product-order', 'common']);

  return (
    <div>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: 16,
          margin: '40px 0 30px 0',
        }}
      >
        <Button
          onClick={copyFirstPurchasePrice}
          label={t('copyFirstPurchasePrice')}
          type="default"
          size="small"
        />
        <MasterDataDropdown
          apiConfig={{
            params: {
              code_kbn: [SelectE.M_CODE_DROPDOWN_EXAMPLE],
              hidden_flg: 0,
            },
          }}
          style={{ width: 240 }}
          placeholder={t('selectPurchasePrice')}
        />
        <span>{t('fromThePurchaseAmountOf')}</span>
        <Button
          label={t('copy')}
          type="primary"
          size="small"
          onClick={copyPurchasePriceFromDropdown}
        />
      </div>
      <EditableTable
        pagination={false}
        data={purchasePrice}
        columns={purchasePriceColumns}
        setData={setPurchasePrice}
      />
    </div>
  );
};

export default PurchasePriceTab;
