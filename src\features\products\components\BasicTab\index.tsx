import { Collapse, Table } from '@/components/atoms';
import { Form, FormFieldConfigI } from '@/components/organisms/Form';
import { FormInstance } from 'antd/es/form';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useProductCategorySettingSection } from '../../hooks/useProductCategorySettingSection';
import { ProductDetailI, TProductTypeI } from '../../types';
import { ProductCategorySettingSection } from './ProductCategorySettingSection';

interface BasicTabPropsI {
  product?: ProductDetailI;
  basicForm: FormInstance;
  basicFormFields: FormFieldConfigI[];
  onChangeProductTypes?: (types: TProductTypeI[]) => void;
}

export default function BasicTab({
  product,
  basicForm,
  basicFormFields,
  onChangeProductTypes,
}: BasicTabPropsI) {
  const { t } = useTranslation('products');
  const [data, setData] = useState<TProductTypeI[]>([]);

  useEffect(() => {
    if (!product) return;
    setData(product.t_product_types ?? []);
  }, [product, basicForm]);

  useEffect(() => {
    onChangeProductTypes?.(data);
  }, [data, onChangeProductTypes]);

  const { columns } = useProductCategorySettingSection(data, setData);

  const handleAdd = () => {
    const newItem: TProductTypeI = {
      id: Date.now(),
      t_pms_product_fkid: 1,
      m_pms_product_type_fkid: 1,
      product_type_fkid: {
        id: 1,
        pms_product_type_name: 'New Item',
        corporate_id: 1,
        m_pms_product_type_group_fkid: 0,
        product_type_group: {
          id: 1,
          pms_product_type_group_name: '新しい分類',
          corporate_id: 1,
          sort_key: 1,
          hidden_flg: 0,
        },
        sort_key: 2,
        hidden_flg: 3,
      },
    };

    setData((prev) => [...prev, newItem]);
  };

  return (
    <div className="mt-10 w-full">
      <Form
        name="basic"
        form={basicForm}
        formFields={basicFormFields}
        numOfColumns={2}
      />

      <div className="mt-10">
        <Collapse
          items={[
            {
              label: t('productCategorySettings'),
              children: (
                <>
                  <ProductCategorySettingSection
                    productNo={product?.pms_product_no || ''}
                    onAddCategory={handleAdd}
                  />
                  <Table<TProductTypeI>
                    columns={columns}
                    dataSource={data}
                    rowKey="id"
                    loadingMinHeight="20px"
                    pagination={false}
                  />
                </>
              ),
            },
          ]}
        />
      </div>
    </div>
  );
}
