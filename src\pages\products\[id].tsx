import { Button, Text } from '@/components/atoms';
import ClientOnlyStyledTabs from '@/components/atoms/Tabs/ClientOnlyStyledTabs';
import BasicTab from '@/features/products/components/BasicTab';
import ColorList from '@/features/products/components/ColorList';
import OrderInformationList from '@/features/products/components/OrderInformation/List';
import { ProductFormFieldKeyE } from '@/features/products/enums';
import { useProductFormSubmit } from '@/features/products/hooks/useProductFormSubmit';
import { useProductDetailInformation } from '@/features/products/hooks/useProductInformationDetail';
import { TProductTypeI } from '@/features/products/types';
import { withMainLayout } from '@/hocs/withMainLayout';
import { DateHelper } from '@/utils';
import { getServerSideTranslationsProps } from '@/utils/localization';
import { Form as AntdForm } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'next-i18next';
import { Fragment, useEffect, useState } from 'react';

interface ProductInformationDetailPagePropsI {
  type: 'create' | 'edit';
}

const ProductInformationDetailPage = ({
  type = 'edit',
}: ProductInformationDetailPagePropsI) => {
  const { t } = useTranslation('products');
  const [basicForm] = AntdForm.useForm();
  const [productTypes, setProductTypes] = useState<TProductTypeI[]>([]);

  const {
    data: product,
    basicFormFields,
    setOnProductCategoryChange,
    setOnProductStoreBudgetChange,
    dataTableMateria,
    setDataTableMateria,
    formInitialValues,
  } = useProductDetailInformation();

  const { handleSubmit } = useProductFormSubmit({
    type,
    product,
    productTypes,
  });

  useEffect(() => {
    if (formInitialValues) {
      basicForm.setFieldsValue(formInitialValues);
    }
  }, [formInitialValues]);

  useEffect(() => {
    setOnProductCategoryChange?.((code) =>
      basicForm.setFieldsValue({
        [ProductFormFieldKeyE.PRODUCT_CATEGORY]: code,
      }),
    );

    setOnProductStoreBudgetChange?.((id) =>
      basicForm.setFieldsValue({
        [ProductFormFieldKeyE.STORE_BUDGET_CATEGORY]: id,
      }),
    );
  }, [setOnProductCategoryChange, setOnProductStoreBudgetChange, basicForm]);

  const renderProductMetaInfo = () => {
    if (type !== 'edit' || !product) return null;

    const createdAt = product.created_at
      ? dayjs(product.created_at).format(DateHelper.FORMAT.FORMAT_24)
      : '';
    const updatedAt = product.updated_at
      ? dayjs(product.updated_at).format(DateHelper.FORMAT.FORMAT_24)
      : '';

    return (
      <Text variant="caption" className="mt-2 block leading-5">
        {t('newRegistrationDateAndTime')}: {createdAt}
        {product.created_by?.name && ` (${product.created_by.name})`}
        <br />
        {t('lastUpdated')}: {updatedAt}
        {product.updated_by?.name && ` (${product.updated_by.name})`}
      </Text>
    );
  };

  const tabs = [
    {
      key: 'basic_info',
      label: t('basicInfo'),
      children: (
        <BasicTab
          product={product}
          basicForm={basicForm}
          basicFormFields={basicFormFields}
          onChangeProductTypes={setProductTypes}
        />
      ),
    },
    {
      key: 'color_size',
      label: t('colorSize'),
      children: (
        <ColorList
          product={product}
          dataTableMateria={dataTableMateria}
          setDataTableMateria={setDataTableMateria}
        />
      ),
      disabled: type === 'create' || !product?.id,
    },
    {
      key: 'order_info',
      label: t('orderInfo'),
      children: (
        <div className="mt-10 w-full">
          <OrderInformationList product={product} />
        </div>
      ),
      disabled: type === 'create' || !product?.id,
    },
  ];

  return (
    <Fragment>
      <Text className="mb-8 text-[24px] leading-[35px] font-bold">
        {t('productDetail')}
      </Text>

      <ClientOnlyStyledTabs
        formType={type}
        defaultActiveKey="basic_info"
        items={tabs}
        className="mt-8 mb-4"
      />

      <Button
        onClick={() => handleSubmit(basicForm, dataTableMateria)}
        type="primary"
        className="mt-8"
        label={type === 'edit' ? t('update') : t('registration')}
      />

      {renderProductMetaInfo()}
    </Fragment>
  );
};

export const getServerSideProps = getServerSideTranslationsProps(['products']);

export default withMainLayout(ProductInformationDetailPage);
