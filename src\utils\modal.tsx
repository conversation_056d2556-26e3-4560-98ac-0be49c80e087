import { Text } from '@/components/atoms';
import { queryClient } from '@/configs/query-client';
import { withThemeProvider } from '@/hocs/withThemeProvider';
import type { ModalProps } from 'antd';
import { Modal } from 'antd';
import { i18n } from 'next-i18next';
import React, { useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import { I18nextProvider } from 'react-i18next';
import { QueryClientProvider } from 'react-query';

let modalCount = 0;

const ThemedModal = withThemeProvider(Modal);

interface CustomModalPropsI extends ModalProps {
  content?: React.ReactNode;
  afterOpen?: () => void;
}

/**
 * openModal - Opens an Ant Design Modal via Portal, supports nesting.
 * @param config - AntD Modal props + custom content.
 * @returns closeModal - Call this to close the modal manually.
 */
export function openModal(config: CustomModalPropsI = {}) {
  const container = document.createElement('div');
  container.id = `modal-container-${modalCount++}`;
  document.body.appendChild(container);
  const root = ReactDOM.createRoot(container);

  const closeModal = () => {
    root.unmount();
    if (container.parentNode) {
      container.parentNode.removeChild(container);
    }
  };

  const ModalWrapper = () => {
    useEffect(() => {
      if (config.afterOpen) {
        config.afterOpen();
      }

      return () => {
        if (config.afterClose) {
          config.afterClose();
        }
      };
    }, []);

    const { content, ...modalProps } = config;

    return (
      <ThemedModal
        {...modalProps}
        open
        onCancel={(e) => {
          if (config.onCancel) config.onCancel(e);
          closeModal();
        }}
        onOk={(e) => {
          if (config.onOk) config.onOk(e);
          closeModal();
        }}
        getContainer={false} // Important: Prevents AntD from attaching modal to body by default
      >
        <QueryClientProvider client={queryClient}>
          {content || null}
        </QueryClientProvider>
      </ThemedModal>
    );
  };

  root.render(
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n!}>
        <ModalWrapper />
      </I18nextProvider>
    </QueryClientProvider>,
  );

  return closeModal;
}

export const handleConfirmSubmit = (message: string) =>
  new Promise<boolean>((resolve) => {
    openModal({
      title: null,
      content: <Text className="text-sm">{message}</Text>,
      okButtonProps: {
        type: 'primary',
      },
      onOk: () => resolve(true),
      onCancel: () => resolve(false),
    });
  });
