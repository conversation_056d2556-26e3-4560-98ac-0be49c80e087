import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { LoadingOutlined } from '@ant-design/icons';
import { Meta, StoryObj } from '@storybook/react-vite';
import { SpinProps } from 'antd';
import { Loading } from './index';

const ThemedLoading = withThemeProvider(Loading);

const meta: Meta<typeof Loading> = {
  title: 'Atoms/Loading',
  component: ThemedLoading,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'ローディングインジケーター（スピナーとも呼ばれる）は、アクションが進行中であることをユーザーに伝える視覚的な手がかりです。これは、アプリケーションがデータをロードしたり、リクエストを処理したり、その他の時間のかかる操作を実行しているときに、ユーザーにフィードバックを提供し、不確実性を軽減するために使用されます。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Loading>;

export const Default: StoryT = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: 'これはローディングコンポーネントのデフォルトの状態です。',
      },
    },
  },
};

export const Small: StoryT = {
  args: {
    size: 'small',
  },
  parameters: {
    docs: {
      description: {
        story: '小さいサイズのローディングスピナー。',
      },
    },
  },
};

export const Large: StoryT = {
  args: {
    size: 'large',
  },
  parameters: {
    docs: {
      description: {
        story: '大きいサイズのローディングスピナー。',
      },
    },
  },
};

export const WithTip: StoryT = {
  args: {
    tip: 'Loading data...',
  },
  parameters: {
    docs: {
      description: {
        story: 'カスタムのヒント/メッセージ付きのローディングスピナー。',
      },
    },
  },
};

export const WithDelay: StoryT = {
  args: {
    delay: 1000,
  },
  parameters: {
    docs: {
      description: {
        story: '1秒の遅延後に表示されるローディングスピナー。',
      },
    },
  },
};

export const CustomIndicator: StoryT = {
  args: {
    indicator: <LoadingOutlined style={{ fontSize: 24 }} spin />,
  },
  parameters: {
    docs: {
      description: {
        story: 'カスタムインジケーターアイコン付きのローディングスピナー。',
      },
    },
  },
};

export const Embedded: StoryT = {
  render: (args: SpinProps) => (
    <ThemedLoading {...args}>
      <div style={{ padding: 20, background: '#f6f6f6', borderRadius: 8 }}>
        <p>Some content is loading...</p>
      </div>
    </ThemedLoading>
  ),
  args: {
    tip: 'Loading content...',
  },
  parameters: {
    docs: {
      description: {
        story: 'コンテンツをラップするローディングスピナー（埋め込みモード）。',
      },
    },
  },
};
