### 📌 Description

- What problem does it solve?
- What feature does it add?
- What refactoring or cleanup is done?

---

### ✅ Summary of Changes

- [ ] Added feature XYZ
- [ ] Fixed bug ABC
- [ ] Refactored DEF for better readability

---

### 📸 Evidences

<!-- Include before/after screenshots if visual changes were made -->

---

### 🔍 How to Test

1. `yarn install && yarn dev`
2. Navigate to `/dashboard`
3. Confirm user profile section renders correctly

---

### 🧩 Related Issues / Tickets

- Closes #123
- Relates to #101

---

### 🙏 Reviewer Checklist

- [ ] Code is clean and well-documented
- [ ] Unit/integration tests added or updated
- [ ] No breaking changes
- [ ] Meets acceptance criteria
