import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { Meta, StoryObj } from '@storybook/react-vite';
import { StyledTabs, Tabs } from './index';

const ThemedTabs = withThemeProvider(Tabs);
const ThemedStyledTabs = withThemeProvider(StyledTabs);

const meta: Meta<typeof Tabs> = {
  title: 'Atoms/Tabs',
  component: ThemedTabs,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'タブは、ユーザーが同じコンテキスト内で異なるビューまたはコンテンツのセクションを切り替えることができるナビゲーション要素です。これらは、大量の関連コンテンツをコンパクトなスペースに整理して表示するのに非常に役立ちます。',
      },
    },
  },
};

export default meta;

export const DefaultTabs: StoryObj<typeof StyledTabs> = {
  render: (args) => <ThemedStyledTabs {...args} />,
  args: {
    items: [
      {
        key: '1',
        label: 'Tab 1',
        children: 'Content of Search Tab',
      },
      {
        key: '2',
        label: 'Tab 2',
        children: 'Content of Integrated Correspondence History Tab',
      },
      {
        key: '3',
        label: 'Tab 3',
        children:
          'Content of Integrated Correspondence History (Separate Tab) Tab',
      },
    ],
  },
  parameters: {
    docs: {
      description: {
        story: '特定のデザインに合わせるためのカスタムスタイルのタブグループ。',
      },
    },
  },
};
