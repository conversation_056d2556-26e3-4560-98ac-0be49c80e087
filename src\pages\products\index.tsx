import {
  But<PERSON>,
  <PERSON>lapse,
  Divider,
  InputSearch,
  Table,
} from '@/components/atoms';
import { AppRouteE } from '@/enums';
import ProductSearchForm from '@/features/product-information/components/ProductSearchForm';
import { useProductInformation } from '@/features/product-information/hooks/useProductInformation';
import { ProductI } from '@/features/product-information/types';
import { withMainLayout } from '@/hocs/withMainLayout';
import usePagination from '@/hooks/usePagination';
import { getServerSideTranslationsProps } from '@/utils/localization';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { useTranslation } from 'next-i18next';
import Link from 'next/link';
import { Fragment, useState } from 'react';

const ProductInformation = () => {
  const { t } = useTranslation('products');
  const { pagination, setDefaultPage } = usePagination();
  const [keyword, setKeyword] = useState('');

  const { data, isFetching, columns, refetch, applyFilters } =
    useProductInformation(
      pagination.current,
      pagination.pageSize,
      setDefaultPage,
    );

  return (
    <Fragment>
      <h1 className="mb-4 text-2xl font-bold">{t('productInformationList')}</h1>

      <div className="mb-4 flex items-center justify-between gap-x-4">
        <InputSearch
          placeholder={t('productCodeNotesInput')}
          allowClear
          enterButton={t('search')}
          value={keyword}
          prefix={<SearchOutlined />}
          onChange={(e) => {
            const value = e.target.value;
            setKeyword(value);
          }}
          onSearch={(value: string) => {
            applyFilters(value, {}, 'normal');
          }}
        />
        <Link href={`/${AppRouteE.PRODUCTS_CREATE}`} target="_blank">
          <Button
            icon={<PlusOutlined />}
            label={t('productInformationAddition')}
            type="primary"
          />
        </Link>
      </div>

      <Collapse
        items={[
          {
            key: '1',
            label: t('advancedSearch'),
            children: (
              <ProductSearchForm
                onSearch={(formValues) =>
                  applyFilters('', formValues, 'advanced')
                }
              />
            ),
          },
        ]}
        className="!mb-4"
      />

      <Divider />

      <Table<ProductI>
        columns={columns}
        dataSource={data?.data}
        rowKey="id"
        pagination={{
          ...pagination,
          total: data?.meta?.total || 0,
        }}
        loading={isFetching}
        onRefresh={refetch}
      />
    </Fragment>
  );
};

export const getServerSideProps = getServerSideTranslationsProps(['products']);

export default withMainLayout(ProductInformation);
