import { Notification } from '@/components/atoms';
import { MESSAGES } from '@/enums/messages';
import { RegexE } from '@/enums/regex';
import { CellComponentI, RowDataI } from '@/types/common';

export const handleValidatePrice = (
  cell: CellComponentI<RowDataI>,
  value: any,
) => {
  if (!value) return true;
  const regex = new RegExp(RegexE.DECIMAL_NUMBER_12_3);
  const isValid = regex.test(value);
  if (!isValid) {
    Notification.error({
      message: MESSAGES.E_INVALID_DECIMAL_FORMAT.replace(
        '{integer}',
        '12',
      ).replace('{decimal}', '3'),
    });
  }

  return isValid;
};
