import { QueryKeysE } from '@/enums/query-keys';
import { useSampleCreateInformation } from '@/features/sample-information/hooks/useSampleInformationCreate';
import { getSample } from '@/features/sample-information/services';
import { useRouter } from 'next/router';
import { useQuery } from 'react-query';

export const useSampleDetailInformation = () => {
  const router = useRouter();
  const { id } = router.query;

  const query = useQuery(
    [QueryKeysE.SAMPLES, id],
    () => getSample(Number(id)),
    {
      enabled: !!id,
    },
  );

  const {
    basicFormFields,
    partnerFormFields,
    handleSubmit,
    handleCollapseAll,
    handleCollapseNone,
    handleDelete,
    dataTableMateria,
    columnsMaterial,
    setDataTableMateria,
  } = useSampleCreateInformation();

  return {
    ...query,
    basicFormFields,
    partnerFormFields,
    handleSubmit,
    handleCollapseAll,
    handleCollapseNone,
    handleDelete,
    dataTableMateria,
    columnsMaterial,
    setDataTableMateria,
  };
};
