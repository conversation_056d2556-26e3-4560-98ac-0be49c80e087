import { Text } from '@/components/atoms';
import { DateRange } from '@/components/atoms/DateRange';
import { FormFieldConfigI } from '@/components/organisms/Form';
import { ApiEndpointE } from '@/enums';
import { SelectE } from '@/enums/select';
import { DateHelper } from '@/utils';
import { useTranslation } from 'next-i18next';
import { useRef } from 'react';
import { ProductCategoryField } from '../components/ProductCategoryModal/ProductCategoryField';
import { StoreBudgetField } from '../components/StoreBudgetTypeModal/StoreBudgetField';
import { ProductFormFieldKeyE } from '../enums';
import { ProductCategoryI, ProductStoreBudgetI } from '../types';
import { useProductCategorySelector } from './useProductCategorySelector';
import { useStoreBudgetSelector } from './useStoreBudgetSelector';

export const useProductCreateInformation = (
  initialProductCategory?: ProductCategoryI,
  initialProductStoreBudget?: ProductStoreBudgetI,
) => {
  const { t } = useTranslation('products');

  const requiredRule = (fieldLabel: string) => ({
    required: true,
    message: t('inputRequired', { nameField: fieldLabel }),
  });
  const commonFieldProps = {
    labelPosition: 'right' as const,
    labelColSpan: 7,
  };

  const productCategoryChangeRef = useRef<(code: string) => void>();
  const setOnProductCategoryChange = (fn: (code: string) => void) => {
    productCategoryChangeRef.current = fn;
  };

  const productStoreBudgetChangeRef = useRef<(id: number) => void>();
  const setOnProductStoreBudgetChange = (fn: (id: number) => void) => {
    productStoreBudgetChangeRef.current = fn;
  };

  const { selected, handleSelect } = useProductCategorySelector(
    initialProductCategory,
    (code) => productCategoryChangeRef.current?.(code),
  );

  const { selectedStore, handleSelectStore } = useStoreBudgetSelector(
    initialProductStoreBudget,
    (id) => productStoreBudgetChangeRef.current?.(id),
  );

  const basicFormFields: FormFieldConfigI[] = [
    {
      label: t('productCode'),
      name: ProductFormFieldKeyE.PRODUCT_CODE,
      type: 'input',
      rules: [requiredRule(t('productCode'))],
      requiredText: t('required'),
      maxLength: 40,
      ...commonFieldProps,
      regexPattern: /^[a-zA-Z0-9@._\-#$&*]*$/,
    },
    {
      label: t('productName'),
      name: ProductFormFieldKeyE.PRODUCT_NAME,
      type: 'input',
      rules: [requiredRule(t('productName'))],
      requiredText: t('required'),
      maxLength: 255,
      ...commonFieldProps,
    },
    {
      label: t('genderCategory'),
      name: ProductFormFieldKeyE.GENDER_CATEGORY,
      type: 'select',
      rules: [requiredRule(t('genderCategory'))],
      initialValue: '',
      apiConfig: {
        endpoint: ApiEndpointE.MASTER_CODE,
        params: { code_kbn: [SelectE.M_CODE_GENDERCATEGORYCD], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      requiredText: t('required'),
      ...commonFieldProps,
    },
    {
      label: t('productCategory'),
      name: ProductFormFieldKeyE.PRODUCT_CATEGORY,
      type: 'input-search',
      rules: [requiredRule(t('productCategory'))],
      render: () => (
        <ProductCategoryField selected={selected} onSelect={handleSelect} />
      ),
      requiredText: t('required'),
      ...commonFieldProps,
    },
    {
      label: t('storeBudgetCategory'),
      name: ProductFormFieldKeyE.STORE_BUDGET_CATEGORY,
      type: 'input-search',
      extra: (
        <Text variant="caption" className="mt-2">
          {t('storeBudgetCategoryDescription')}
        </Text>
      ),
      render: () => (
        <StoreBudgetField
          selected={selectedStore}
          onSelect={handleSelectStore}
        />
      ),
      rules: [requiredRule(t('storeBudgetCategory'))],
      requiredText: t('required'),
      ...commonFieldProps,
    },
    {
      label: t('salesPeriod'),
      name: ProductFormFieldKeyE.SALES_PERIOD,
      type: 'datepicker-range',
      render: () => (
        <DateRange
          fromName={ProductFormFieldKeyE.SALES_PLAN_START}
          toName={ProductFormFieldKeyE.SALES_PLAN_END}
          placeholder={[t('startDate'), t('endDate')]}
          format={DateHelper.FORMAT.FORMAT_2}
        />
      ),
      ...commonFieldProps,
    },
    {
      label: t('storeArrivalDate'),
      name: ProductFormFieldKeyE.STORE_WITHDRAW_DATE,
      type: 'datepicker',
      placeholder: t('storeArrivalDate'),
      format: DateHelper.FORMAT.FORMAT_2,
      ...commonFieldProps,
    },
    {
      label: t('salesSeasonCode'),
      name: ProductFormFieldKeyE.SALES_SEASON_CODE,
      type: 'input',
      maxLength: 40,
      extra: (
        <Text variant="caption" className="mt-2">
          {t('salesSeasonCodeDescription')}
        </Text>
      ),
      ...commonFieldProps,
    },
    {
      label: t('sampleNumber'),
      name: ProductFormFieldKeyE.SAMPLE_NO,
      type: 'input',
      maxLength: 50,
      ...commonFieldProps,
      regexPattern: /^[a-zA-Z0-9@._\-#$&*]*$/,
    },
    {
      label: t('buyer'),
      name: ProductFormFieldKeyE.PLANNER_CODE,
      type: 'select',
      apiConfig: {
        endpoint: ApiEndpointE.MASTER_CODE,
        params: { code_kbn: [SelectE.M_CODE_BUYER], hidden_flg: 0 },
        mapFields: {
          label: SelectE.M_CODE_FIELD_CODE_NAME,
          value: SelectE.M_CODE_FIELD_CODE,
        },
      },
      ...commonFieldProps,
    },
    {
      label: t('hsCode'),
      name: ProductFormFieldKeyE.HS_CODE,
      type: 'input',
      maxLength: 20,
      ...commonFieldProps,
      regexPattern: /^[a-zA-Z0-9@._\-#$&*]*$/,
    },
    {
      label: t('notes'),
      name: ProductFormFieldKeyE.REMARKS,
      type: 'textarea',
      maxLength: 10000,
      ...commonFieldProps,
    },
  ];

  return {
    basicFormFields,
    setOnProductCategoryChange,
    setOnProductStoreBudgetChange,
  };
};
