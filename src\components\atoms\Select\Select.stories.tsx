import { withThemeProvider } from '@/hocs/withThemeProvider';
import '@/styles/globals.css';
import { Meta, StoryObj } from '@storybook/react-vite';
import { Select } from './index';

const ThemedSelect = withThemeProvider(Select);

const meta: Meta<typeof Select> = {
  title: 'Atoms/Select',
  component: ThemedSelect,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'ドロップダウンメニューとしても知られるセレクトコンポーネントは、ユーザーがリストから1つ以上のオプションを選択できるグラフィカルユーザーインターフェース要素です。フォームや設定ページで一般的な要素であり、選択肢のリストをコンパクトに表示する方法を提供します。',
      },
    },
  },
};

export default meta;

type StoryT = StoryObj<typeof Select>;

export const Default: StoryT = {
  args: {
    options: [
      { value: 'jack', label: 'Jack' },
      { value: 'lucy', label: 'Lucy' },
      { value: 'Yiminghe', label: 'yiminghe' },
      { value: 'disabled', label: 'Disabled', disabled: true },
    ],
  },
  parameters: {
    docs: {
      description: {
        story:
          'デフォルトのセレクトコンポーネントでは、ユーザーがリストから1つのオプションを選択できます。',
      },
    },
  },
};

export const DisabledWithValue: StoryT = {
  args: {
    ...Default.args,
    disabled: true,
    defaultValue: 'lucy',
  },
  parameters: {
    docs: {
      description: {
        story:
          '無効なセレクトコンポーネントは事前に選択された値を持つことができますが、ユーザーはそれを変更できません。',
      },
    },
  },
};

export const Disabled: StoryT = {
  args: {
    ...Default.args,
    disabled: true,
  },
  parameters: {
    docs: {
      description: {
        story: '操作できない無効なセレクトコンポーネント。',
      },
    },
  },
};

export const Multiple: StoryT = {
  args: {
    mode: 'multiple',
    placeholder: 'Please select',
    defaultValue: ['option1', 'option2'],
    options: [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
      { value: 'option3', label: 'Option 3' },
      { value: 'option4', label: 'Option 4' },
      { value: 'option5', label: 'Option 5' },
      { value: 'option6', label: 'Option 6' },
      { value: 'option7', label: 'Option 7' },
      { value: 'option8', label: 'Option 8' },
      { value: 'option9', label: 'Option 9' },
      { value: 'option10', label: 'Option 10' },
      { value: 'option11', label: 'Option 11' },
      { value: 'option12', label: 'Option 12' },
      { value: 'option13', label: 'Option 13' },
      { value: 'option14', label: 'Option 14' },
      { value: 'option15', label: 'Option 15' },
      { value: 'option16', label: 'Option 16' },
      { value: 'option17', label: 'Option 17' },
      { value: 'option18', label: 'Option 18' },
      { value: 'option19', label: 'Option 19' },
      { value: 'option20', label: 'Option 20' },
      { value: 'option21', label: 'Option 21' },
      { value: 'option22', label: 'Option 22' },
      { value: 'option23', label: 'Option 23' },
      { value: 'option24', label: 'Option 24' },
      { value: 'option25', label: 'Option 25' },
      { value: 'option26', label: 'Option 26' },
      { value: 'option27', label: 'Option 27' },
      { value: 'option28', label: 'Option 28' },
      { value: 'option29', label: 'Option 29' },
      { value: 'option30', label: 'Option 30' },
      { value: 'option31', label: 'Option 31' },
      { value: 'option32', label: 'Option 32' },
      { value: 'option33', label: 'Option 33' },
      { value: 'option34', label: 'Option 34' },
      { value: 'option35', label: 'Option 35' },
      { value: 'option36', label: 'Option 36' },
      { value: 'option37', label: 'Option 37' },
      { value: 'option38', label: 'Option 38' },
      { value: 'option39', label: 'Option 39' },
      { value: 'option40', label: 'Option 40' },
      { value: 'option41', label: 'Option 41' },
      { value: 'option42', label: 'Option 42' },
      { value: 'option43', label: 'Option 43' },
      { value: 'option44', label: 'Option 44' },
      { value: 'option45', label: 'Option 45' },
      { value: 'option46', label: 'Option 46' },
      { value: 'option47', label: 'Option 47' },
      { value: 'option48', label: 'Option 48' },
      { value: 'option49', label: 'Option 49' },
      { value: 'option50', label: 'Option 50' },
    ],
  },
  parameters: {
    docs: {
      description: {
        story: '複数のオプションを選択できるセレクトコンポーネント。',
      },
    },
  },
};

export const Loading: StoryT = {
  args: {
    ...Default.args,
    loading: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          'オプションが取得中であることを示すローディング状態のセレクトコンポーネント。',
      },
    },
  },
};

export const Search: StoryT = {
  args: {
    ...Default.args,
    showSearch: true,
    placeholder: 'Search to select',
  },
  parameters: {
    docs: {
      description: {
        story:
          'オプションをフィルタリングできる検索入力付きのセレクトコンポーネント。',
      },
    },
  },
};

export const AllowClear: StoryT = {
  args: {
    ...Default.args,
    allowClear: true,
    placeholder: 'Select a value',
  },
  parameters: {
    docs: {
      description: {
        story:
          '選択を簡単にリセットできるクリアボタン付きのセレクトコンポーネント。',
      },
    },
  },
};
