import { QueryKeysE } from '@/enums/query-keys';
import { useProductCreateInformation } from '@/features/products/hooks/useProductInformationCreate';
import { getProduct } from '@/features/products/services';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import { useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { StoreBudgetTypeI } from '../types';

export const useProductDetailInformation = () => {
  const router = useRouter();
  const { id } = router.query;

  const [dataTableMateria, setDataTableMateria] = useState<
    Array<StoreBudgetTypeI>
  >([]);

  const query = useQuery(
    [QueryKeysE.PRODUCTS, id],
    () => getProduct(Number(id)),
    {
      enabled: !!id,
    },
  );

  const product = query.data;
  const productCategory = product?.product_category ?? undefined;
  const productStoreBudget = product?.store_budget_type ?? undefined;

  const {
    basicFormFields,
    setOnProductCategoryChange,
    setOnProductStoreBudgetChange,
  } = useProductCreateInformation(productCategory, productStoreBudget);

  const formInitialValues = useMemo(() => {
    if (!product) return undefined;

    return {
      ...product,
      pms_sales_plan_start_datetime: dayjs(
        product.pms_sales_plan_start_datetime,
      ),
      pms_sales_plan_end_datetime: dayjs(product.pms_sales_plan_end_datetime),
      pms_store_withdraw_datetime: dayjs(product.pms_store_withdraw_datetime),
      product_category_code: productCategory?.code ?? '',
      store_budget_type_id: productStoreBudget?.id ?? '',
    };
  }, [product]);

  return {
    ...query,
    basicFormFields,
    setOnProductCategoryChange,
    dataTableMateria,
    setDataTableMateria,
    setOnProductStoreBudgetChange,
    formInitialValues,
  };
};
