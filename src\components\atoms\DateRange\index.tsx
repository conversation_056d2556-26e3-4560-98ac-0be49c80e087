import { DateHelper } from '@/utils';
import { DatePicker as AntdDatePicker, Form } from 'antd';
import { Dayjs } from 'dayjs';
import { useTranslation } from 'next-i18next';
import { styled } from 'styled-components';

const { Item: FormItem } = Form;

interface DateRangePropsI {
  fromName: string;
  toName: string;
  format?: string;
  placeholder?: [string, string];
  initialValueFrom?: Dayjs;
  initialValueTo?: Dayjs;
}

const StyledDatePicker = styled(AntdDatePicker)`
  box-shadow: var(--inner-box-shadow);
  .ant-picker-prefix {
    color: #858c90;
  }
`;

export const DateRange = ({
  fromName,
  toName,
  format = DateHelper.FORMAT.FORMAT_2,
  placeholder,
}: DateRangePropsI) => {
  const { t } = useTranslation('sample-information');
  const [placeholderFrom, placeholderTo] = placeholder ?? [
    t('startDate'),
    t('endDate'),
  ];

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
      <FormItem name={fromName} noStyle>
        <StyledDatePicker
          placeholder={placeholderFrom}
          format={format}
          style={{ width: '50%' }}
        />
      </FormItem>

      <span>〜</span>

      <FormItem name={toName} noStyle>
        <StyledDatePicker
          placeholder={placeholderTo}
          format={format}
          style={{ width: '50%' }}
        />
      </FormItem>
    </div>
  );
};
