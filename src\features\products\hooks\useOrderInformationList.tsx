import { Image, Text } from '@/components/atoms';
import { QueryKeysE } from '@/enums/query-keys';
import usePagination from '@/hooks/usePagination';
import { DateHelper, StringUtil } from '@/utils';
import { message } from 'antd';
import { ColumnType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { getOrderInformation } from '../services';
import { OrderInformationDataTableI, OrderInformationI } from '../types';

const useOrderInformationList = (): {
  pagination: any;
  isLoading: boolean;
  isFetching: boolean;
  columns: ColumnType<OrderInformationDataTableI>[];
  data: OrderInformationDataTableI[];
  total: number | undefined;
} => {
  const router = useRouter();
  const { id } = router.query;
  const { t } = useTranslation('products');
  const { pagination } = usePagination();
  const { isLoading, data, isFetching } = useQuery(
    [
      QueryKeysE.STORE_BUDGET_TYPES,
      pagination.current,
      pagination.pageSize,
      id,
    ],
    () =>
      getOrderInformation({
        page: pagination.current,
        per_page: pagination.pageSize,
        id: id?.toString(),
      }),
    {
      onSuccess: (data) => {
        if (!data?.data?.length) {
          message.warning(t('noDataFound'));

          return;
        }
      },
    },
  );

  const total = useMemo(() => data?.meta.total, [data]);

  const tableData = useMemo(
    () =>
      data?.data.reduce(
        (s: OrderInformationDataTableI[], order: OrderInformationI, idx) => {
          const { pms_shippings, ...rest } = order;
          if (pms_shippings?.length) {
            const items = pms_shippings.map((i, iIdx) => ({
              ...rest,
              numberOfShipments: pms_shippings.length,
              shipping: i,
              idx,
              isFirst: iIdx === 0,
              id: i.id,
            }));

            return [...s, ...items];
          }

          const defaultShipping: OrderInformationDataTableI = {
            ...rest,
            numberOfShipments: 1,
            isFirst: true,
            idx,
          };

          return [...s, defaultShipping];
        },
        [] as OrderInformationDataTableI[],
      ) || [],
    [data?.data],
  );

  const onCell = (record: OrderInformationDataTableI) => ({
    rowSpan: record.isFirst ? record.numberOfShipments : 0,
  });

  const renderNumberOfOrders = (_: any, record: OrderInformationDataTableI) => (
    <div className="flex flex-col justify-center">
      <Text
        className="cursor-pointer text-sm underline"
        style={{ color: 'var(--link-text-table)' }}
      >
        {t('orderTimes', { count: record.pms_order_cnt })}
      </Text>
    </div>
  );

  const renderOrderDate = (_: any, record: OrderInformationDataTableI) => (
    <div className="flex flex-col justify-center">
      <Text className="text-sm">
        {dayjs(record.pms_order_datetime).format(DateHelper.FORMAT.FORMAT_2)}
      </Text>
      <Text className="text-sm">{record.pms_order_sbt_code}</Text>
    </div>
  );

  const renderNumberOfShipments = (
    _: any,
    record: OrderInformationDataTableI,
  ) => (
    <div className="flex flex-col justify-center">
      <Text
        className="cursor-pointer text-sm underline"
        style={{ color: 'var(--link-text-table)' }}
      >
        {record.shipping?.id
          ? t('orderTimes', { count: record.shipping?.pms_shipping_cnt })
          : t('Unregistered')}
      </Text>
    </div>
  );

  const renderShippingDate = (_: any, record: OrderInformationDataTableI) => {
    if (!record.shipping?.local_shipping_plan_datetime) {
      return (
        <div className="flex flex-col justify-center">
          <Text className="text-sm">-</Text>
        </div>
      );
    }

    return (
      <div className="flex flex-col justify-center">
        <Text className="text-sm">
          {dayjs(record.shipping?.local_shipping_plan_datetime).format(
            DateHelper.FORMAT.FORMAT_2,
          )}
          (4)
        </Text>
        <Text className="text-sm">
          {record.shipping?.local_shipping_result_datetime
            ? `${dayjs(record.shipping?.local_shipping_result_datetime).format(
                DateHelper.FORMAT.FORMAT_2,
              )}(4)`
            : '-'}
        </Text>
      </div>
    );
  };

  const renderArrivalDate = (_: any, record: OrderInformationDataTableI) => {
    if (!record.shipping?.store_arrival_plan_datetime) {
      return (
        <div className="flex flex-col justify-center">
          <Text className="text-sm">-</Text>
        </div>
      );
    }

    return (
      <div className="flex flex-col justify-center">
        <Text className="text-sm">
          {`${dayjs(record.shipping?.store_arrival_plan_datetime).format(
            DateHelper.FORMAT.FORMAT_2,
          )}(2)`}
        </Text>
        <Text className="text-sm">
          {record.shipping?.store_arrival_result_datetime
            ? `${dayjs(record.shipping?.store_arrival_result_datetime).format(
                DateHelper.FORMAT.FORMAT_2,
              )}(2)`
            : '-'}
        </Text>
      </div>
    );
  };

  const renderPurchaseOrder = (_: any, record: OrderInformationDataTableI) => {
    if (record.inspect_pre_datetime) {
      return (
        <div className="flex flex-col justify-center">
          <Text className="text-sm">
            {dayjs(record.inspect_pre_datetime).format(
              DateHelper.FORMAT.FORMAT_2,
            )}
          </Text>
          <div className="flex items-center justify-center gap-2">
            <Text className="text-sm">{`●${t('well')}`}</Text>
            <Text className="text-sm">{`●${t('lang')}`}</Text>
          </div>
          <Text className="text-sm">{record.import_method_code}</Text>
        </div>
      );
    }

    return (
      <div className="flex flex-col justify-center">
        <Text className="text-sm">{t('Unsent')}</Text>
      </div>
    );
  };

  const columns: ColumnType<OrderInformationDataTableI>[] = [
    {
      title: t('no'),
      key: 'id',
      render: (_: any, record: OrderInformationDataTableI) => record.idx + 1,
      onCell,
      width: 50,
      align: 'center',
      className: 'bg-[#F6F6F6]',
    },
    {
      title: t('numberOfOrders'),
      key: 'numberOfOrders',
      render: renderNumberOfOrders,
      dataIndex: 'numberOfOrders',
      width: 100,
      align: 'center',
      onCell,
    },
    {
      title: (
        <div className="flex flex-col justify-center">
          <Text className="text-sm">{t('orderDate')}</Text>
          <Text className="text-sm">{t('orderType')}</Text>
        </div>
      ),
      key: 'orderDate',
      dataIndex: 'orderDate',
      align: 'center',
      onCell,
      render: renderOrderDate,
    },
    {
      title: t('image'),
      key: 'image',
      dataIndex: 'image',
      align: 'center',
      onCell,
      render: (_: any, record: OrderInformationDataTableI) => (
        <div className="flex items-center justify-center">
          <Image
            width={48}
            height={48}
            src={record.media_file_path || '/images/no_image.avif'}
            alt="sample"
          />
        </div>
      ),
    },
    {
      title: t('businessPartner'),
      key: 'businessPartner',
      dataIndex: 'businessPartner',
      align: 'center',
      onCell,
      render: (_: any, record: OrderInformationDataTableI) => (
        <div className="flex flex-col justify-start text-left">
          <Text className="text-sm">{record.pms_manager_code}</Text>
        </div>
      ),
    },
    {
      title: t('retailPriceIncludingTax'),
      key: 'retailPriceIncludingTax',
      dataIndex: 'retailPriceIncludingTax',
      align: 'center',
      onCell,
      render: (_: any, record: OrderInformationDataTableI) => (
        <div className="flex flex-col justify-end text-right">
          <Text className="text-sm">
            {StringUtil.formatCurrency(record.payment_kgn)}
          </Text>
        </div>
      ),
    },
    {
      title: t('cost'),
      key: 'cost',
      dataIndex: 'cost',
      align: 'center',
      onCell,
      render: (_: any, record: OrderInformationDataTableI) => (
        <div className="flex flex-col justify-center text-center">
          <Text className="text-sm">
            {`${Number(record.pms_jodai_tax_in).toFixed(1)}USD`}
          </Text>
          <Text className="text-sm">
            {record.pms_jodai_tax_out &&
              StringUtil.formatNumberCurrency(
                Number(record.pms_jodai_tax_out),
              ) + '円'}
          </Text>
        </div>
      ),
    },
    {
      title: (
        <div className="flex flex-col items-center justify-center">
          <Text className="text-sm">
            {`${t('purchaseOrder')} /${t('specification')} `}
          </Text>
          <Text className="text-sm">{t('lastSentDateAndTime')}</Text>
        </div>
      ),
      key: 'purchaseOrder',
      dataIndex: 'purchaseOrder',
      align: 'center',
      onCell,
      render: renderPurchaseOrder,
    },
    {
      title: t('numberOfShipments'),
      key: 'numberOfShipments',
      dataIndex: 'numberOfShipments',
      align: 'center',
      render: renderNumberOfShipments,
    },
    {
      title: (
        <div className="flex flex-col justify-center">
          <Text className="text-sm">{t('localPort')}</Text>
          <Text className="text-sm">{t('shippingDate')}</Text>
        </div>
      ),
      key: 'shippingDate',
      dataIndex: 'shippingDate',
      align: 'center',
      render: renderShippingDate,
    },
    {
      title: (
        <div className="flex flex-col justify-center">
          <Text className="text-sm">{t('japanStore')}</Text>
          <Text className="text-sm">{t('arrivalDate')}</Text>
        </div>
      ),
      key: 'arrivalDate',
      dataIndex: 'arrivalDate',
      align: 'center',
      render: renderArrivalDate,
    },
    {
      title: (
        <div className="flex flex-col justify-center">
          <Text className="text-sm">{t('exchange')}</Text>
          <Text className="text-sm">{t('rate')}</Text>
        </div>
      ),
      key: 'rate',
      dataIndex: 'rate',
      align: 'center',
      render: (_: any, record: OrderInformationDataTableI) => (
        <div className="flex flex-col justify-end">
          <Text className="text-right text-sm">{`${record?.shipping?.fec_rate ? StringUtil.formatNumberCurrency(record?.shipping?.fec_rate, 2) + '円' : '-'}`}</Text>
        </div>
      ),
    },
    {
      title: t('shipmentQuantity'),
      key: 'shipmentQuantity',
      dataIndex: 'shipmentQuantity',
      align: 'center',
      render: (_: any, record: OrderInformationDataTableI) => (
        <div className="flex flex-col justify-end">
          <Text className="text-right text-sm">{`${record?.shipping?.local_shipping_plan_week_no_n ? record?.shipping?.local_shipping_plan_week_no_n : '-'}`}</Text>
        </div>
      ),
    },
    {
      title: t('shippingAmount'),
      key: 'shippingAmount',
      dataIndex: 'shippingAmount',
      align: 'center',
      render: (_: any, record: OrderInformationDataTableI) => (
        <div className="flex flex-col justify-end">
          <Text className="text-right text-sm">{`${record?.shipping?.local_shipping_plan_week_no_n && record?.shipping?.fec_rate ? StringUtil.formatNumberCurrency(record?.shipping?.local_shipping_plan_week_no_n * record?.shipping?.fec_rate, 2) + '円' : '-'}`}</Text>
        </div>
      ),
    },
  ];

  return { pagination, isLoading, isFetching, columns, data: tableData, total };
};

export default useOrderInformationList;
