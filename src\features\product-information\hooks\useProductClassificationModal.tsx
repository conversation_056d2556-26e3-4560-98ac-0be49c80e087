import { Checkbox, Text } from '@/components/atoms';
import { QueryKeysE } from '@/enums/query-keys';
import { ProductClassificationDataI } from '@/features/product-information/types';
import { message } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useTranslation } from 'next-i18next';
import { useEffect, useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { ProductFormFieldKeyE } from '../enums';
import { getProductClassifications } from '../services';

export const useProductClassificationModal = (
  pagination: {
    page: number;
    pageSize: number;
  },
  setDefaultPage: () => void,
  initialSelectedValues?: ProductClassificationDataI[],
) => {
  const { t } = useTranslation('products');

  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedItems, setSelectedItems] = useState<
    ProductClassificationDataI[]
  >([]);

  const { page, pageSize } = pagination;

  // Initialize with the passed initial values
  useEffect(() => {
    if (initialSelectedValues && initialSelectedValues.length > 0) {
      const initialKeys = initialSelectedValues.map((item) => item.id);
      setSelectedRowKeys(initialKeys);
      setSelectedItems(initialSelectedValues);
    }
  }, [initialSelectedValues]);

  const columns: ColumnsType<ProductClassificationDataI> = useMemo(
    () => [
      {
        title: t('no'),
        key: ProductFormFieldKeyE.NO,
        align: 'center',
        width: 60,
        className: 'first-column-bg',
        render: (_: any, _record: ProductClassificationDataI, index: number) =>
          (page - 1) * pageSize + index + 1,
      },
      {
        title: t('selection'),
        key: ProductFormFieldKeyE.SELECTION,
        align: 'center',
        width: 80,
        render: (_: any, record: ProductClassificationDataI) => (
          <Checkbox
            checked={selectedRowKeys.includes(record.id)}
            onChange={(e) => {
              if (e.target.checked) {
                setSelectedRowKeys([...selectedRowKeys, record.id]);
                setSelectedItems([...selectedItems, record]);
              } else {
                setSelectedRowKeys(
                  selectedRowKeys.filter((key) => key !== record.id),
                );
                setSelectedItems(
                  selectedItems.filter((item) => item.id !== record.id),
                );
              }
            }}
          />
        ),
      },
      {
        title: t('productClassificationGroup'),
        dataIndex: ProductFormFieldKeyE.GROUP,
        key: ProductFormFieldKeyE.GROUP,
        align: 'center',
        render: (value: string) => (
          <Text className="max-3-rows-truncate text-left text-sm">
            {value}{' '}
          </Text>
        ),
      },
      {
        title: t('productClassification'),
        dataIndex: ProductFormFieldKeyE.CLASSIFICATION,
        key: ProductFormFieldKeyE.CLASSIFICATION,
        align: 'center',
        render: (value: string) => (
          <Text className="max-3-rows-truncate text-left text-sm">
            {value}{' '}
          </Text>
        ),
      },
    ],
    [t, selectedRowKeys, selectedItems, page, pageSize],
  );

  const query = useQuery(
    [QueryKeysE.PRODUCT_CLASSIFICATION, page, pageSize, searchKeyword],
    () =>
      getProductClassifications(page, pageSize, {
        search: searchKeyword,
      }),
    {
      onSuccess: (data) => {
        if (!data?.data?.length) {
          message.warning(t('noDataFound'));

          return;
        }
      },
    },
  );

  const applyFilters = (keyword: string) => {
    setDefaultPage();
    setSearchKeyword(keyword.trim());
  };

  return {
    ...query,
    columns,
    applyFilters,
    selectedRowKeys,
    selectedItems,
  };
};
