import { Meta, StoryObj } from '@storybook/react-vite';
import MainLayout from '.';

export default {
  title: 'Templates/MainLayout',
  component: MainLayout,
} as Meta<typeof MainLayout>;

type StoryT = StoryObj<typeof MainLayout>;

export const Default: StoryT = {
  args: {
    children: (
      <div className="rounded-lg bg-white p-4 shadow">
        Main Content Goes Here
      </div>
    ),
  },
};
