/**
 * Prevents users from entering invalid characters into the input.
 * @param e - The InputEvent from onBeforeInput
 * @param pattern - A RegExp used to validate each input character
 */
export function preventInvalidInput(
  e: React.FormEvent<HTMLInputElement> & { data: string },
  pattern?: RegExp,
) {
  const input = (e as unknown as InputEvent).data;
  if (!input || !(pattern instanceof RegExp)) return;

  if (!pattern.test(input)) {
    e.preventDefault();
  }
}

/**
 * Prevents users from pasting invalid content into the input.
 * @param e - The ClipboardEvent from onPaste
 * @param pattern - A RegExp used to validate the pasted content
 */

export function preventInvalidPaste(
  e: React.ClipboardEvent<HTMLInputElement>,
  pattern?: RegExp,
) {
  const pasted = e.clipboardData.getData('text');
  if (!pasted || !(pattern instanceof RegExp)) return;

  if (!pattern.test(pasted)) {
    e.preventDefault();
  }
}
